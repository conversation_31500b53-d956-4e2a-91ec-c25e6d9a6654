import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Edit3, 
  FileText, 
  CheckCircle, 
  Clock, 
  Users, 
  BarChart3,
  TrendingUp,
  Eye,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';

const EditorDashboard: React.FC = () => {
  const { user } = useAuth();

  const editorStats = [
    {
      name: 'Content Edited',
      value: '47',
      change: '+12%',
      trend: 'up',
      icon: Edit3,
      color: 'bg-green-50 text-green-600 border-green-200'
    },
    {
      name: 'Reports Moderated',
      value: '23',
      change: '+8%',
      trend: 'up',
      icon: CheckCircle,
      color: 'bg-blue-50 text-blue-600 border-blue-200'
    },
    {
      name: 'Pending Reviews',
      value: '5',
      change: '-3',
      trend: 'down',
      icon: Clock,
      color: 'bg-orange-50 text-orange-600 border-orange-200'
    },
    {
      name: 'Content Views',
      value: '12.4K',
      change: '+24%',
      trend: 'up',
      icon: Eye,
      color: 'bg-purple-50 text-purple-600 border-purple-200'
    }
  ];

  const recentActivity = [
    {
      id: '1',
      action: 'Published Article',
      title: 'Emergency Preparedness Guide 2024',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      type: 'publish'
    },
    {
      id: '2',
      action: 'Moderated Report',
      title: 'Flood Report - Downtown Area',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      type: 'moderate'
    },
    {
      id: '3',
      action: 'Updated Content',
      title: 'Safety Resources Page',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      type: 'edit'
    }
  ];

  const pendingTasks = [
    {
      id: '1',
      title: 'Review Tornado Safety Guide',
      priority: 'High',
      dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      type: 'review'
    },
    {
      id: '2',
      title: 'Moderate User Report #247',
      priority: 'Medium',
      dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      type: 'moderate'
    },
    {
      id: '3',
      title: 'Update Partner Information',
      priority: 'Low',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      type: 'update'
    }
  ];

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Editor Dashboard</h1>
          <p className="text-gray-600">Welcome back, {user?.name}! Manage content and moderate reports.</p>
        </div>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          <Link
            to="/editor/content"
            className="bg-green-600 text-white px-4 py-2 rounded-xl hover:bg-green-700 transition-colors font-medium"
          >
            Manage Content
          </Link>
          <Link
            to="/editor/reports"
            className="border border-gray-300 text-gray-700 px-4 py-2 rounded-xl hover:bg-gray-50 transition-colors font-medium"
          >
            Moderate Reports
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {editorStats.map((stat, index) => (
          <div key={index} className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl ${stat.color}`}>
                <stat.icon size={24} />
              </div>
              <div className={`flex items-center text-sm font-medium ${
                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUp size={16} className="mr-1" />
                {stat.change}
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
            <p className="text-gray-600 text-sm">{stat.name}</p>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Recent Activity</h2>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-xl">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  activity.type === 'publish' ? 'bg-green-100 text-green-600' :
                  activity.type === 'moderate' ? 'bg-blue-100 text-blue-600' :
                  'bg-purple-100 text-purple-600'
                }`}>
                  {activity.type === 'publish' ? <CheckCircle size={20} /> :
                   activity.type === 'moderate' ? <Users size={20} /> :
                   <Edit3 size={20} />}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{activity.action}</p>
                  <p className="text-sm text-gray-600">{activity.title}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {activity.timestamp.toLocaleTimeString()} - {activity.timestamp.toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Pending Tasks */}
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Pending Tasks</h2>
          <div className="space-y-4">
            {pendingTasks.map((task) => (
              <div key={task.id} className="p-4 border border-gray-200 rounded-xl hover:shadow-sm transition-shadow">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900">{task.title}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    task.priority === 'High' ? 'bg-red-100 text-red-800' :
                    task.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {task.priority}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">{task.type}</span>
                  <span className="text-xs text-gray-500">
                    Due: {task.dueDate.toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
          <Link
            to="/editor/tasks"
            className="block mt-4 text-center text-green-600 hover:text-green-700 font-medium"
          >
            View All Tasks
          </Link>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/editor/content/new"
            className="p-6 border-2 border-dashed border-gray-300 rounded-xl hover:border-green-400 hover:bg-green-50 transition-all duration-200 text-center group"
          >
            <FileText size={32} className="mx-auto text-gray-400 group-hover:text-green-600 mb-3" />
            <h3 className="font-medium text-gray-900 group-hover:text-green-700">Create Content</h3>
            <p className="text-sm text-gray-600 mt-1">Add new articles or guides</p>
          </Link>
          
          <Link
            to="/editor/reports"
            className="p-6 border-2 border-dashed border-gray-300 rounded-xl hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 text-center group"
          >
            <CheckCircle size={32} className="mx-auto text-gray-400 group-hover:text-blue-600 mb-3" />
            <h3 className="font-medium text-gray-900 group-hover:text-blue-700">Moderate Reports</h3>
            <p className="text-sm text-gray-600 mt-1">Review pending reports</p>
          </Link>
          
          <Link
            to="/editor/analytics"
            className="p-6 border-2 border-dashed border-gray-300 rounded-xl hover:border-purple-400 hover:bg-purple-50 transition-all duration-200 text-center group"
          >
            <BarChart3 size={32} className="mx-auto text-gray-400 group-hover:text-purple-600 mb-3" />
            <h3 className="font-medium text-gray-900 group-hover:text-purple-700">View Analytics</h3>
            <p className="text-sm text-gray-600 mt-1">Check content performance</p>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default EditorDashboard;