# Component Documentation

## 🧩 Overview

This document provides detailed information about the React components used in the DisasterWatch platform, including their props, usage examples, and implementation details.

## 🏗️ Component Architecture

### Component Categories
- **Layout Components**: Header, Footer, Navigation
- **Authentication Components**: Login, Registration, Protected Routes
- **Admin Components**: Dashboard, User Management, Analytics
- **Moderator Components**: Report Verification, Content Moderation
- **Common Components**: Buttons, Cards, Modals, Forms
- **Map Components**: Interactive maps and location pickers
- **Chat Components**: Real-time communication system

## 🔐 Authentication Components

### LoginModal
**Location**: `src/components/Auth/LoginModal.tsx`

**Description**: Modal component for user authentication

**Props**:
```typescript
interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}
```

**Usage**:
```tsx
<LoginModal 
  isOpen={showLogin} 
  onClose={() => setShowLogin(false)}
  onSuccess={() => navigate('/dashboard')}
/>
```

**Features**:
- Email/password authentication
- Role-based redirect after login
- Error handling and validation
- Loading states
- Responsive design

### ProtectedRoute
**Location**: `src/components/Auth/ProtectedRoute.tsx`

**Description**: Route wrapper for permission-based access control

**Props**:
```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  userOnly?: boolean;
  adminOnly?: boolean;
  moderatorOnly?: boolean;
}
```

**Usage**:
```tsx
<ProtectedRoute requiredPermission="reports.create">
  <CreateReport />
</ProtectedRoute>
```

## 🏠 Layout Components

### Header
**Location**: `src/components/Layout/Header.tsx`

**Description**: Main navigation header with user authentication

**Features**:
- Responsive navigation menu
- User authentication status
- Role-based menu items
- Mobile hamburger menu
- Search functionality
- Notification indicators

### AdminLayout
**Location**: `src/components/Admin/AdminLayout.tsx`

**Description**: Layout wrapper for admin and moderator pages

**Features**:
- Sidebar navigation
- Role-based menu items
- User profile section
- Logout functionality
- Mobile responsive sidebar

## 📊 Admin Components

### AdminDashboard
**Location**: `src/pages/Admin/AdminDashboard.tsx`

**Description**: Main admin dashboard with system overview

**Features**:
- Real-time metrics display
- Interactive charts and graphs
- System health monitoring
- Quick action buttons
- Content management shortcuts

**Key Metrics**:
- Total users and reports
- System performance indicators
- Traffic analytics
- Content statistics

### UserManagement
**Location**: `src/pages/Admin/UserManagement.tsx`

**Description**: Comprehensive user administration interface

**Features**:
- User listing with search and filters
- Role assignment and permissions
- Account suspension and activation
- Bulk operations
- User activity monitoring
- Export functionality

**Props**:
```typescript
interface UserManagementProps {
  initialFilters?: {
    role?: string;
    status?: string;
    search?: string;
  };
}
```

### ContentManagement
**Location**: `src/pages/Admin/ContentManagement.tsx`

**Description**: Content publishing and management system

**Features**:
- Content creation and editing
- Publishing workflow
- Content scheduling
- Version control
- SEO optimization
- Media management

## 🛡️ Moderator Components

### ModeratorDashboard
**Location**: `src/pages/Admin/ModeratorDashboard.tsx`

**Description**: Moderator workflow dashboard for report management

**Features**:
- Pending reports queue
- Priority-based sorting
- Quick verification actions
- Statistics overview
- Filter and search capabilities

**Key Statistics**:
- Pending reports count
- Daily verification metrics
- Average processing time
- Flagged content count

### ReportVerification
**Location**: `src/pages/Admin/ReportVerification.tsx`

**Description**: Detailed report verification interface

**Props**:
```typescript
interface ReportVerificationProps {
  reportId: string;
}
```

**Features**:
- Report details display
- Photo gallery with zoom
- External data integration
- Verification decision tools
- Evidence collection
- Confidence scoring
- Notes and comments

**Verification Process**:
1. Review report details
2. Check external data sources
3. Analyze submitted evidence
4. Make verification decision
5. Add verification notes
6. Submit decision

### ModeratorUserManagement
**Location**: `src/pages/Admin/ModeratorUserManagement.tsx`

**Description**: User moderation tools for content management

**Features**:
- User violation tracking
- Suspension management
- Flag system for problematic users
- Activity monitoring
- Risk assessment tools

## 🗺️ Map Components

### LocationPicker
**Location**: `src/components/Map/LocationPicker.tsx`

**Description**: Interactive map for location selection

**Props**:
```typescript
interface LocationPickerProps {
  onLocationSelect: (location: {
    lat: number;
    lng: number;
    address: string;
  }) => void;
  initialLocation?: {
    lat: number;
    lng: number;
  };
  zoom?: number;
}
```

**Features**:
- Click-to-select location
- Address geocoding
- Current location detection
- Zoom controls
- Map layer options

**Usage**:
```tsx
<LocationPicker
  onLocationSelect={(location) => setFormLocation(location)}
  initialLocation={{ lat: 16.8661, lng: 96.1951 }}
  zoom={10}
/>
```

## 💬 Chat Components

### ChatWidget
**Location**: `src/components/Chat/ChatWidget.tsx`

**Description**: Real-time chat interface for user communication

**Props**:
```typescript
interface ChatWidgetProps {
  isOpen: boolean;
  onToggle: () => void;
  recipientId?: string;
  conversationId?: string;
}
```

**Features**:
- Real-time messaging
- Emoji support
- File attachments
- Message status indicators
- Typing indicators
- Message history

### EmojiPicker
**Location**: `src/components/Chat/EmojiPicker.tsx`

**Description**: Emoji selection component for chat

**Features**:
- Categorized emoji display
- Search functionality
- Recent emoji tracking
- Custom emoji support

## 🎨 Common Components

### DisasterReportCard
**Location**: `src/components/Common/DisasterReportCard.tsx`

**Description**: Card component for displaying disaster report summaries

**Props**:
```typescript
interface DisasterReportCardProps {
  disasterType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  location: string;
  reportedTime: string;
  affectedPeople: number;
  resources: {
    medical: number;
    foodWater: number;
    shelter: number;
  };
  emergencyContacts: string[];
}
```

**Features**:
- Severity color coding
- Resource allocation display
- Emergency contact information
- Responsive design
- Action buttons

### ViewReportsButton
**Location**: `src/components/Common/ViewReportsButton.tsx`

**Description**: Interactive button component for report navigation

**Features**:
- Hover animations
- Loading states
- Icon integration
- Accessibility support

### SystemStatus
**Location**: `src/components/Common/SystemStatus.tsx`

**Description**: System health indicator component

**Features**:
- Real-time status updates
- Color-coded indicators
- Detailed metrics display
- Alert notifications

## 📝 Form Components

### CreateReport
**Location**: `src/pages/CreateReport.tsx`

**Description**: Multi-step form for disaster report creation

**Features**:
- Step-by-step wizard interface
- Form validation
- Photo upload with preview
- Location selection
- Assistance type selection
- Progress indicators

**Form Steps**:
1. Disaster type selection
2. Location and details
3. Photo upload
4. Assistance requirements
5. Review and submit

**Validation Rules**:
- Required fields validation
- Location accuracy verification
- Photo size and format limits
- Description length requirements

## 🔧 Utility Components

### LoadingSpinner
**Description**: Reusable loading indicator

**Props**:
```typescript
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}
```

### ErrorBoundary
**Description**: Error handling wrapper component

**Features**:
- Graceful error handling
- Error reporting
- Fallback UI display
- Development error details

### Modal
**Description**: Reusable modal component

**Props**:
```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnOverlayClick?: boolean;
}
```

## 🎯 Component Best Practices

### TypeScript Usage
- All components use TypeScript interfaces
- Proper prop typing
- Generic components where applicable
- Strict type checking enabled

### Performance Optimization
- React.memo for expensive components
- useCallback for event handlers
- useMemo for computed values
- Lazy loading for large components

### Accessibility
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Color contrast compliance

### Testing
- Unit tests for all components
- Integration tests for complex workflows
- Accessibility testing
- Visual regression testing

### Code Organization
- Single responsibility principle
- Reusable component patterns
- Consistent naming conventions
- Proper file structure
- Documentation comments

## 🔄 State Management

### Context Usage
- AuthContext for authentication state
- AdminContext for admin-specific state
- Local state for component-specific data
- Props drilling minimization

### State Patterns
- Controlled components for forms
- Uncontrolled components for simple inputs
- State lifting for shared data
- Custom hooks for complex logic

## 📱 Responsive Design

### Breakpoints
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

### Mobile Optimizations
- Touch-friendly interface elements
- Optimized image loading
- Simplified navigation
- Gesture support
- Performance considerations

## 🎨 Styling Guidelines

### Tailwind CSS Usage
- Utility-first approach
- Consistent spacing scale
- Color system adherence
- Responsive modifiers
- Custom component classes

### Design Tokens
- Consistent color palette
- Typography scale
- Spacing system
- Border radius standards
- Shadow system
