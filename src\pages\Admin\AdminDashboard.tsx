import React from 'react';
import { Link } from 'react-router-dom';
import { 
  TrendingUp, 
  Users, 
  FileText, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  Eye, 
  Globe,
  Activity,
  Shield,
  Zap,
  BarChart3,
  Database,
  Server,
  FileSearch
} from 'lucide-react';
import { useAdmin } from '../../context/AdminContext';
import { BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { format } from 'date-fns';
import SystemStatus from '../../components/Common/SystemStatus';

const AdminDashboard: React.FC = () => {
  const { analytics, metrics, contentSections } = useAdmin();

  const quickStats = [
    {
      name: 'Total Visitors',
      value: analytics?.visitors.total.toLocaleString() || '0',
      change: `+${analytics?.visitors.trend || 0}%`,
      trend: 'up',
      icon: Users,
      color: 'bg-blue-50 text-blue-600 border-blue-200'
    },
    {
      name: 'Page Views',
      value: analytics?.engagement.pageViews.toLocaleString() || '0',
      change: `+${analytics?.engagement.trend || 0}%`,
      trend: 'up',
      icon: Eye,
      color: 'bg-green-50 text-green-600 border-green-200'
    },
    {
      name: 'Conversion Rate',
      value: `${analytics?.conversions.rate || 0}%`,
      change: `+${analytics?.conversions.trend || 0}%`,
      trend: 'up',
      icon: TrendingUp,
      color: 'bg-purple-50 text-purple-600 border-purple-200'
    },
    {
      name: 'Avg. Session',
      value: `${Math.floor((analytics?.engagement.avgSessionDuration || 0) / 60)}m ${(analytics?.engagement.avgSessionDuration || 0) % 60}s`,
      change: '+8.2%',
      trend: 'up',
      icon: Clock,
      color: 'bg-orange-50 text-orange-600 border-orange-200'
    }
  ];

  const systemHealth = [
    {
      name: 'Server Status',
      status: 'Operational',
      icon: Shield,
      color: 'text-green-600'
    },
    {
      name: 'Database',
      status: 'Healthy',
      icon: Activity,
      color: 'text-green-600'
    },
    {
      name: 'CDN',
      status: 'Fast',
      icon: Zap,
      color: 'text-green-600'
    },
    {
      name: 'API',
      status: 'Responsive',
      icon: Globe,
      color: 'text-green-600'
    }
  ];

  const recentActivity = metrics?.recentActivity || [];
  const publishedContent = contentSections.filter(section => section.isPublished).length;
  const draftContent = contentSections.filter(section => !section.isPublished).length;

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Overview</h1>
          <p className="text-gray-600">Monitor your platform performance and manage content</p>
        </div>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          <div className="bg-white rounded-xl px-4 py-2 border border-gray-200 shadow-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700">System Healthy</span>
            </div>
          </div>
          <Link
            to="/admin/system-report"
            className="bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700 transition-colors font-medium flex items-center"
          >
            <FileSearch size={16} className="mr-2" />
            System Report
          </Link>
          <a
            href="/"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors font-medium"
          >
            View Live Site
          </a>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => (
          <div key={index} className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl ${stat.color}`}>
                <stat.icon size={24} />
              </div>
              <div className={`flex items-center text-sm font-medium ${
                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUp size={16} className="mr-1" />
                {stat.change}
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
            <p className="text-gray-600 text-sm">{stat.name}</p>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Analytics Charts */}
        <div className="lg:col-span-2 space-y-6">
          {/* Traffic Sources */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Traffic Sources</h2>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analytics?.traffic.sources || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name} ${value}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {(analytics?.traffic.sources || []).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Device Analytics */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Device Usage</h2>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={analytics?.traffic.devices || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#4A90E2" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* System Health */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">System Health</h2>
            <SystemStatus
              status="operational"
              services={[
                { name: 'API Server', status: 'up', responseTime: 32 },
                { name: 'Database', status: 'up', responseTime: 15 },
                { name: 'File Storage', status: 'up', responseTime: 28 },
                { name: 'Network', status: 'up', responseTime: 9 }
              ]}
            />
          </div>

          {/* Content Status */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Content Status</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <CheckCircle size={20} className="text-green-600" />
                  <span className="text-gray-700">Published</span>
                </div>
                <span className="text-lg font-semibold text-gray-900">{publishedContent}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <FileText size={20} className="text-orange-600" />
                  <span className="text-gray-700">Drafts</span>
                </div>
                <span className="text-lg font-semibold text-gray-900">{draftContent}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Clock size={20} className="text-blue-600" />
                  <span className="text-gray-700">Scheduled</span>
                </div>
                <span className="text-lg font-semibold text-gray-900">2</span>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Recent Activity</h2>
            <div className="space-y-4">
              {recentActivity.slice(0, 5).map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`w-2 h-2 rounded-full mt-2 ${
                    activity.type === 'content' ? 'bg-blue-500' :
                    activity.type === 'user' ? 'bg-green-500' :
                    activity.type === 'system' ? 'bg-purple-500' :
                    'bg-orange-500'
                  }`}></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-xs text-gray-600">{activity.details}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {format(activity.timestamp, 'MMM d, HH:mm')} by {activity.user}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
            <div className="space-y-3">
              <Link
                to="/admin/system-report"
                className="w-full bg-red-600 text-white py-3 px-4 rounded-xl hover:bg-red-700 transition-colors font-medium flex items-center justify-center"
              >
                <FileSearch size={16} className="mr-2" />
                Generate System Report
              </Link>
              <Link
                to="/admin/content"
                className="w-full border border-gray-200 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 transition-colors font-medium flex items-center justify-center"
              >
                <FileText size={16} className="mr-2" />
                Manage Content
              </Link>
              <Link
                to="/admin/users"
                className="w-full border border-gray-200 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 transition-colors font-medium flex items-center justify-center"
              >
                <Users size={16} className="mr-2" />
                User Management
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Top Locations */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Top Visitor Locations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {(analytics?.traffic.locations || []).map((location, index) => (
            <div key={index} className="text-center p-4 bg-gray-50 rounded-xl">
              <h3 className="font-semibold text-gray-900 mb-1">{location.country}</h3>
              <p className="text-2xl font-bold text-blue-600 mb-1">{location.visitors.toLocaleString()}</p>
              <p className="text-xs text-gray-600">visitors</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
