# Deployment Guide

## 🚀 Overview

This guide covers the deployment process for the DisasterWatch platform across different environments and hosting platforms.

## 📋 Prerequisites

### System Requirements

- **Node.js**: Version 16.x or higher
- **npm**: Version 8.x or higher (or yarn 1.22+)
- **Git**: For version control
- **Modern Browser**: Chrome 90+, Firefox 88+, Safari 14+

### Development Tools

- **Code Editor**: VS Code recommended
- **Terminal**: Command line access
- **Package Manager**: npm or yarn

## 🏗️ Build Process

### Production Build

```bash
# Install dependencies
npm install

# Run build process
npm run build

# Preview build locally (optional)
npm run preview
```

### Build Output

The build process creates a `dist/` directory containing:

- `index.html` - Main HTML file
- `assets/` - Compiled CSS, JS, and static assets
- Static files and images

### Build Optimization

- **Code Splitting**: Automatic chunk splitting for better loading
- **Tree Shaking**: Unused code elimination
- **Minification**: CSS and JavaScript compression
- **Asset Optimization**: Image compression and optimization

## 🌐 Environment Configuration

### Environment Variables

Create environment-specific `.env` files:

#### `.env.development`

```env
VITE_APP_ENV=development
VITE_API_BASE_URL=http://localhost:3001/api
VITE_MAP_API_KEY=your_development_map_key
VITE_ANALYTICS_ID=dev_analytics_id
VITE_ENABLE_MOCK_DATA=true
```

#### `.env.production`

```env
VITE_APP_ENV=production
VITE_API_BASE_URL=https://api.disasterwatch.com
VITE_MAP_API_KEY=your_production_map_key
VITE_ANALYTICS_ID=prod_analytics_id
VITE_ENABLE_MOCK_DATA=false
VITE_SENTRY_DSN=your_sentry_dsn
```

#### `.env.staging`

```env
VITE_APP_ENV=staging
VITE_API_BASE_URL=https://staging-api.disasterwatch.com
VITE_MAP_API_KEY=your_staging_map_key
VITE_ANALYTICS_ID=staging_analytics_id
VITE_ENABLE_MOCK_DATA=false
```

### Configuration Management

- Use environment-specific configuration files
- Implement feature flags for gradual rollouts
- Secure sensitive configuration data
- Version control configuration templates

## ☁️ Deployment Platforms

### 1. Vercel (Recommended)

#### Setup Steps

1. **Connect Repository**

   ```bash
   # Install Vercel CLI
   npm i -g vercel

   # Login to Vercel
   vercel login

   # Deploy from project directory
   vercel
   ```

2. **Configuration**
   Create `vercel.json`:

   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "framework": "vite",
     "rewrites": [
       {
         "source": "/(.*)",
         "destination": "/index.html"
       }
     ],
     "headers": [
       {
         "source": "/assets/(.*)",
         "headers": [
           {
             "key": "Cache-Control",
             "value": "public, max-age=31536000, immutable"
           }
         ]
       }
     ]
   }
   ```

3. **Environment Variables**
   Set in Vercel dashboard or via CLI:
   ```bash
   vercel env add VITE_API_BASE_URL production
   vercel env add VITE_MAP_API_KEY production
   ```

#### Vercel Features

- **Automatic Deployments**: Git-based deployments
- **Preview Deployments**: Branch-based previews
- **Edge Network**: Global CDN distribution
- **Analytics**: Built-in performance monitoring

### 2. Netlify

#### Setup Steps

1. **Build Settings**

   - Build command: `npm run build`
   - Publish directory: `dist`

2. **Redirects Configuration**
   Create `public/_redirects`:

   ```
   /*    /index.html   200
   ```

3. **Headers Configuration**
   Create `public/_headers`:

   ```
   /assets/*
     Cache-Control: public, max-age=31536000, immutable

   /*
     X-Frame-Options: DENY
     X-XSS-Protection: 1; mode=block
     X-Content-Type-Options: nosniff
   ```

#### Netlify Features

- **Form Handling**: Built-in form processing
- **Functions**: Serverless function support
- **Identity**: User authentication service
- **Split Testing**: A/B testing capabilities

### 3. AWS S3 + CloudFront

#### Setup Steps

1. **S3 Bucket Configuration**

   ```bash
   # Create S3 bucket
   aws s3 mb s3://disasterwatch-app

   # Configure bucket for static hosting
   aws s3 website s3://disasterwatch-app \
     --index-document index.html \
     --error-document index.html
   ```

2. **Upload Build Files**

   ```bash
   # Sync build files to S3
   aws s3 sync dist/ s3://disasterwatch-app \
     --delete \
     --cache-control "public, max-age=31536000" \
     --exclude "index.html"

   # Upload index.html with no-cache
   aws s3 cp dist/index.html s3://disasterwatch-app/index.html \
     --cache-control "no-cache"
   ```

3. **CloudFront Distribution**
   ```json
   {
     "Origins": [
       {
         "DomainName": "disasterwatch-app.s3.amazonaws.com",
         "Id": "S3-disasterwatch-app",
         "S3OriginConfig": {
           "OriginAccessIdentity": ""
         }
       }
     ],
     "DefaultCacheBehavior": {
       "TargetOriginId": "S3-disasterwatch-app",
       "ViewerProtocolPolicy": "redirect-to-https"
     },
     "CustomErrorResponses": [
       {
         "ErrorCode": 404,
         "ResponseCode": 200,
         "ResponsePagePath": "/index.html"
       }
     ]
   }
   ```

### 4. Docker Deployment

#### Dockerfile

```dockerfile
# Build stage
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy build files
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Cache static assets
        location /assets/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
    }
}
```

#### Docker Commands

```bash
# Build image
docker build -t disasterwatch-app .

# Run container
docker run -p 80:80 disasterwatch-app

# Docker Compose
docker-compose up -d
```

## 🔒 Security Configuration

### HTTPS Setup

- **SSL Certificate**: Use Let's Encrypt or commercial certificates
- **HSTS Headers**: Enforce HTTPS connections
- **Secure Cookies**: Configure secure cookie settings

### Content Security Policy

```html
<meta
  http-equiv="Content-Security-Policy"
  content="default-src 'self'; 
               script-src 'self' 'unsafe-inline'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: https:; 
               connect-src 'self' https://api.disasterwatch.com;"
/>
```

### Security Headers

```
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: geolocation=(), microphone=(), camera=()
```

## 📊 Monitoring & Analytics

### Performance Monitoring

- **Core Web Vitals**: LCP, FID, CLS tracking
- **Error Tracking**: Sentry integration
- **Performance Metrics**: Real User Monitoring (RUM)

### Analytics Setup

```typescript
// Google Analytics 4
gtag("config", "GA_MEASUREMENT_ID", {
  page_title: document.title,
  page_location: window.location.href,
});

// Custom event tracking
gtag("event", "report_created", {
  event_category: "engagement",
  event_label: "disaster_report",
});
```

### Health Checks

```typescript
// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION,
  });
});
```

## 🔄 CI/CD Pipeline

### GitHub Actions

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Build application
        run: npm run build
        env:
          VITE_API_BASE_URL: ${{ secrets.API_BASE_URL }}
          VITE_MAP_API_KEY: ${{ secrets.MAP_API_KEY }}

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: "--prod"
```

### Deployment Checklist

- [ ] Environment variables configured
- [ ] Build process successful
- [ ] Tests passing
- [ ] Security headers configured
- [ ] SSL certificate installed
- [ ] Monitoring setup
- [ ] Backup strategy implemented
- [ ] Rollback plan prepared

## 🚨 Troubleshooting

### Common Issues

#### Build Failures

```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version
npm --version
```

#### Routing Issues

- Ensure SPA routing is configured
- Check server redirects for 404 errors
- Verify base URL configuration

#### Environment Variables

- Prefix with `VITE_` for client-side access
- Check variable names and values
- Verify environment-specific configurations

#### Performance Issues

- Analyze bundle size with `npm run build -- --analyze`
- Implement code splitting
- Optimize images and assets
- Enable compression

### Debugging Tools

- **Browser DevTools**: Network, Performance, Console
- **Lighthouse**: Performance auditing
- **Webpack Bundle Analyzer**: Bundle size analysis
- **Sentry**: Error tracking and monitoring

## 📈 Performance Optimization

### Build Optimization

- **Code Splitting**: Route-based and component-based
- **Tree Shaking**: Remove unused code
- **Minification**: Compress CSS and JavaScript
- **Asset Optimization**: Image compression and WebP conversion

### Runtime Optimization

- **Lazy Loading**: Components and routes
- **Memoization**: React.memo and useMemo
- **Virtual Scrolling**: Large lists optimization
- **Service Workers**: Caching strategies

### CDN Configuration

- **Static Assets**: Serve from CDN
- **Cache Headers**: Optimize caching strategies
- **Compression**: Enable gzip/brotli compression
- **Geographic Distribution**: Multi-region deployment

## 📚 Additional Resources

### Documentation Links

- [Vite Documentation](https://vitejs.dev/)
- [React Deployment Guide](https://create-react-app.dev/docs/deployment/)
- [Vercel Documentation](https://vercel.com/docs)
- [Netlify Documentation](https://docs.netlify.com/)

### Support Channels

- GitHub Issues for bug reports
- Discord community for discussions
- Email support for enterprise customers
- Documentation wiki for detailed guides
