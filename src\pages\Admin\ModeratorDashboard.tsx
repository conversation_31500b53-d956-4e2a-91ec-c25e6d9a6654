import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Eye, 
  Filter,
  Search,
  MapPin,
  Calendar,
  User,
  ExternalLink,
  Flag,
  Shield,
  TrendingUp,
  Activity
} from 'lucide-react';
import { format } from 'date-fns';
import { Report, ReportVerification } from '../../types';

interface ModeratorStats {
  pendingReports: number;
  verifiedToday: number;
  averageVerificationTime: number;
  flaggedReports: number;
  totalProcessed: number;
}

const ModeratorDashboard: React.FC = () => {
  const [pendingReports, setPendingReports] = useState<Report[]>([]);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState<ModeratorStats>({
    pendingReports: 0,
    verifiedToday: 12,
    averageVerificationTime: 18,
    flaggedReports: 3,
    totalProcessed: 247
  });

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockPendingReports: Report[] = [
      {
        id: '1',
        title: 'Severe Flooding in Downtown Area',
        disasterType: 'Natural',
        disasterDetail: 'Flash Flood',
        description: 'Heavy rainfall has caused severe flooding in the downtown business district. Water levels are approximately 2-3 feet deep on Main Street.',
        location: {
          lat: 40.7589,
          lng: -73.9851,
          address: 'Downtown Manhattan, New York, NY',
          accuracy: 95
        },
        photos: [
          {
            id: '1',
            url: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=800',
            thumbnail: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=200',
            caption: 'Main Street flooding',
            metadata: {
              size: 2048000,
              format: 'jpeg',
              dimensions: { width: 1920, height: 1080 },
              uploadedAt: new Date()
            }
          }
        ],
        assistanceNeeded: ['Emergency Shelter', 'Food & Water', 'Medical Aid'],
        assistanceDescription: 'Immediate need for temporary shelter for affected families and emergency supplies.',
        status: 'pending',
        priority: 'high',
        reporterId: '2',
        reporterName: 'Sarah Johnson',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        assistanceLog: [],
        metadata: {
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          submissionMethod: 'web'
        }
      },
      {
        id: '2',
        title: 'Wildfire Approaching Residential Area',
        disasterType: 'Natural',
        disasterDetail: 'Wildfire',
        description: 'Fast-moving wildfire is approaching the Oak Hills residential neighborhood. Evacuation may be necessary.',
        location: {
          lat: 34.0522,
          lng: -118.2437,
          address: 'Oak Hills, Los Angeles, CA',
          accuracy: 88
        },
        photos: [
          {
            id: '2',
            url: 'https://images.pexels.com/photos/266487/pexels-photo-266487.jpeg?auto=compress&cs=tinysrgb&w=800',
            thumbnail: 'https://images.pexels.com/photos/266487/pexels-photo-266487.jpeg?auto=compress&cs=tinysrgb&w=200',
            caption: 'Smoke visible from neighborhood',
            metadata: {
              size: 1856000,
              format: 'jpeg',
              dimensions: { width: 1600, height: 900 },
              uploadedAt: new Date()
            }
          }
        ],
        assistanceNeeded: ['Evacuation Support', 'Pet Care', 'Transportation'],
        assistanceDescription: 'Need help with evacuation transportation for elderly residents and pet care.',
        status: 'pending',
        priority: 'critical',
        reporterId: '3',
        reporterName: 'Mike Davis',
        createdAt: new Date(Date.now() - 45 * 60 * 1000),
        updatedAt: new Date(Date.now() - 45 * 60 * 1000),
        assistanceLog: [],
        metadata: {
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          submissionMethod: 'mobile'
        }
      }
    ];

    setPendingReports(mockPendingReports);
    setStats(prev => ({ ...prev, pendingReports: mockPendingReports.length }));
  }, []);

  const filteredReports = pendingReports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.location.address.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPriority = filterPriority === 'all' || report.priority === filterPriority;
    const matchesType = filterType === 'all' || report.disasterType === filterType;
    return matchesSearch && matchesPriority && matchesType;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical': return <AlertTriangle size={16} className="text-red-600" />;
      case 'high': return <AlertTriangle size={16} className="text-orange-600" />;
      case 'medium': return <Clock size={16} className="text-yellow-600" />;
      case 'low': return <CheckCircle size={16} className="text-green-600" />;
      default: return <Clock size={16} className="text-gray-600" />;
    }
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Moderator Dashboard</h1>
          <p className="text-gray-600">Review and verify disaster reports</p>
        </div>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          <div className="bg-white rounded-xl px-4 py-2 border border-gray-200 shadow-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700">Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Reports</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pendingReports}</p>
            </div>
            <div className="p-3 bg-orange-100 text-orange-600 rounded-xl">
              <Clock size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Verified Today</p>
              <p className="text-2xl font-bold text-gray-900">{stats.verifiedToday}</p>
            </div>
            <div className="p-3 bg-green-100 text-green-600 rounded-xl">
              <CheckCircle size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Time</p>
              <p className="text-2xl font-bold text-gray-900">{stats.averageVerificationTime}m</p>
            </div>
            <div className="p-3 bg-blue-100 text-blue-600 rounded-xl">
              <Activity size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Flagged</p>
              <p className="text-2xl font-bold text-gray-900">{stats.flaggedReports}</p>
            </div>
            <div className="p-3 bg-red-100 text-red-600 rounded-xl">
              <Flag size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Processed</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalProcessed}</p>
            </div>
            <div className="p-3 bg-purple-100 text-purple-600 rounded-xl">
              <TrendingUp size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search reports..."
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="relative">
            <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              <option value="all">All Priorities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>
          <div className="relative">
            <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              <option value="all">All Types</option>
              <option value="Natural">Natural Disasters</option>
              <option value="Non-Natural">Non-Natural Disasters</option>
            </select>
          </div>
        </div>
      </div>

      {/* Reports Queue */}
      <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Pending Verification Queue</h2>
          <p className="text-sm text-gray-600 mt-1">Reports waiting for verification - sorted by priority and time</p>
        </div>
        <div className="divide-y divide-gray-200">
          {filteredReports.map((report) => (
            <div 
              key={report.id} 
              className="p-6 hover:bg-gray-50 transition-colors cursor-pointer"
              onClick={() => setSelectedReport(report)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    {getPriorityIcon(report.priority)}
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(report.priority)}`}>
                      {report.priority}
                    </span>
                    <span className="text-sm text-gray-500">{report.disasterDetail}</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{report.title}</h3>
                  <p className="text-gray-600 mb-3 line-clamp-2">{report.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <MapPin size={16} />
                      <span>{report.location.address}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <User size={16} />
                      <span>{report.reporterName}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar size={16} />
                      <span>{getTimeAgo(report.createdAt)}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                    <Eye size={16} />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                    <CheckCircle size={16} />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                    <XCircle size={16} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ModeratorDashboard;
