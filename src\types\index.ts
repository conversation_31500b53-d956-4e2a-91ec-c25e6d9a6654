export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role?: 'user' | 'volunteer' | 'organization';
  isBlacklisted?: boolean;
  blacklistReason?: string;
  trustScore?: number;
  joinedAt?: Date;
  lastActive?: Date;
  phoneNumber?: string;
  location?: {
    city: string;
    state: string;
    country: string;
  };
}

export interface Report {
  id: string;
  title: string;
  disasterType: 'Natural' | 'Non-Natural';
  disasterDetail: string;
  description: string;
  location: {
    lat: number;
    lng: number;
    address: string;
    accuracy?: number;
  };
  photos: ReportPhoto[];
  assistanceNeeded: string[];
  assistanceDescription: string;
  status: 'pending' | 'verified' | 'rejected' | 'fake' | 'hidden';
  priority: 'low' | 'medium' | 'high' | 'critical';
  reporterId: string;
  reporterName: string;
  createdAt: Date;
  updatedAt: Date;
  assistanceLog: AssistanceEntry[];
  verificationData?: {
    verifiedBy?: string;
    verifiedAt?: Date;
    verificationNotes?: string;
    externalDataChecked?: boolean;
    confidenceScore?: number;
  };
  moderationData?: {
    flagged?: boolean;
    flagReason?: string;
    flaggedBy?: string;
    flaggedAt?: Date;
    hidden?: boolean;
    hiddenBy?: string;
    hiddenAt?: Date;
    hiddenReason?: string;
  };
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    deviceInfo?: string;
    submissionMethod?: 'web' | 'mobile' | 'api';
  };
}

export interface ReportPhoto {
  id: string;
  url: string;
  thumbnail?: string;
  caption?: string;
  metadata?: {
    size: number;
    format: string;
    dimensions?: {
      width: number;
      height: number;
    };
    exif?: Record<string, any>;
    uploadedAt: Date;
  };
}

export interface AssistanceEntry {
  id: string;
  providerId: string;
  providerName: string;
  description: string;
  createdAt: Date;
  endorsed: boolean;
  type: 'offer' | 'delivery' | 'completion' | 'update';
  status: 'pending' | 'accepted' | 'completed' | 'cancelled';
  contactInfo?: {
    phone?: string;
    email?: string;
    preferredMethod?: 'phone' | 'email' | 'platform';
  };
  estimatedCompletion?: Date;
  actualCompletion?: Date;
  feedback?: {
    rating: number;
    comment: string;
    providedBy: string;
    providedAt: Date;
  };
  moderatorNotes?: string;
}

// Enhanced types for better disaster management
export interface EmergencyContact {
  id: string;
  name: string;
  organization: string;
  role: string;
  phone: string;
  email: string;
  availableHours: string;
  specialties: string[];
  coverage: {
    regions: string[];
    disasterTypes: string[];
  };
}

export interface DisasterAlert {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  affectedAreas: string[];
  issuedBy: string;
  issuedAt: Date;
  expiresAt?: Date;
  instructions: string[];
  relatedReports: string[];
  officialSource?: string;
  status: 'active' | 'expired' | 'cancelled';
}

export interface Partner {
  id: string;
  name: string;
  type: 'NGO' | 'Government' | 'Corporate';
  description: string;
  website: string;
  logo: string;
}

export interface SafetyGuide {
  id: string;
  title: string;
  category: string;
  content: string;
  lastUpdated: Date;
}