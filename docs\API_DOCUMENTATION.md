# API Documentation

## 🔗 Overview

This document outlines the API structure and data models used in the DisasterWatch platform. Currently, the platform uses mock data for development, but this documentation serves as a blueprint for future backend integration.

## 📊 Data Models

### User Model
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role?: 'user' | 'volunteer' | 'organization' | 'admin' | 'editor' | 'moderator';
  isBlacklisted?: boolean;
  blacklistReason?: string;
  trustScore?: number;
  joinedAt?: Date;
  lastActive?: Date;
  phoneNumber?: string;
  location?: {
    city: string;
    state: string;
    country: string;
  };
}
```

### Report Model
```typescript
interface Report {
  id: string;
  title: string;
  disasterType: 'Natural' | 'Non-Natural';
  disasterDetail: string;
  description: string;
  location: {
    lat: number;
    lng: number;
    address: string;
    accuracy?: number;
  };
  photos: ReportPhoto[];
  assistanceNeeded: string[];
  assistanceDescription: string;
  status: 'pending' | 'verified' | 'rejected' | 'fake' | 'hidden';
  priority: 'low' | 'medium' | 'high' | 'critical';
  reporterId: string;
  reporterName: string;
  createdAt: Date;
  updatedAt: Date;
  assistanceLog: AssistanceEntry[];
  verificationData?: {
    verifiedBy?: string;
    verifiedAt?: Date;
    verificationNotes?: string;
    externalDataChecked?: boolean;
    confidenceScore?: number;
  };
}
```

### Admin User Model
```typescript
interface AdminUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'moderator' | 'editor' | 'viewer';
  permissions: Permission[];
  lastLogin: Date;
  isActive: boolean;
  createdAt: Date;
  suspendedUntil?: Date;
  suspensionReason?: string;
  violationHistory: UserViolation[];
  loginHistory: LoginRecord[];
}
```

## 🔐 Authentication Endpoints

### POST /auth/login
**Description**: Authenticate user and create session

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response**:
```json
{
  "success": true,
  "user": {
    "id": "user-123",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "user",
    "permissions": ["reports.create", "reports.view"],
    "sessionToken": "jwt-token-here"
  }
}
```

### POST /auth/logout
**Description**: Invalidate user session

**Headers**: `Authorization: Bearer <token>`

**Response**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

## 📝 Report Management Endpoints

### GET /api/reports
**Description**: Retrieve reports with filtering and pagination

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `status`: Filter by status (pending, verified, rejected, fake, hidden)
- `priority`: Filter by priority (low, medium, high, critical)
- `disasterType`: Filter by disaster type (Natural, Non-Natural)
- `search`: Search term for title/description
- `location`: Geographic bounds for location filtering

**Response**:
```json
{
  "success": true,
  "data": {
    "reports": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

### POST /api/reports
**Description**: Create a new disaster report

**Request Body**:
```json
{
  "disasterType": "Natural",
  "disasterDetail": "Earthquake",
  "description": "Strong earthquake felt in downtown area",
  "location": {
    "lat": 16.8661,
    "lng": 96.1951,
    "address": "Yangon, Myanmar"
  },
  "assistanceNeeded": ["medical", "shelter"],
  "assistanceDescription": "Need immediate medical assistance",
  "photos": ["base64-encoded-image-data"]
}
```

### GET /api/reports/:id
**Description**: Get detailed report information

**Response**:
```json
{
  "success": true,
  "data": {
    "report": {...},
    "assistanceLog": [...],
    "verificationData": {...}
  }
}
```

### PUT /api/reports/:id/verify
**Description**: Verify or reject a report (Moderator only)

**Request Body**:
```json
{
  "status": "verified",
  "verificationNotes": "Verified through external sources",
  "confidenceScore": 85,
  "externalDataChecked": true
}
```

## 👥 User Management Endpoints

### GET /api/admin/users
**Description**: Get users list with filtering (Admin only)

**Query Parameters**:
- `page`: Page number
- `limit`: Items per page
- `role`: Filter by role
- `status`: Filter by status (active, suspended, flagged)
- `search`: Search by name or email

### POST /api/admin/users/:id/suspend
**Description**: Suspend a user account (Admin/Moderator only)

**Request Body**:
```json
{
  "reason": "Violation of community guidelines",
  "duration": 24,
  "notes": "Multiple fake reports submitted"
}
```

### POST /api/admin/users/:id/flag
**Description**: Flag a user for review (Moderator only)

**Request Body**:
```json
{
  "reason": "Suspicious activity",
  "severity": "medium",
  "notes": "Multiple reports from same location"
}
```

## 📊 Analytics Endpoints

### GET /api/admin/analytics
**Description**: Get platform analytics (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalReports": 1247,
      "verifiedReports": 892,
      "activeUsers": 3456,
      "responseTime": "< 2hrs"
    },
    "traffic": {
      "pageViews": 15420,
      "uniqueVisitors": 3240,
      "sources": [...]
    },
    "reports": {
      "byType": [...],
      "byStatus": [...],
      "timeline": [...]
    }
  }
}
```

### GET /api/admin/metrics
**Description**: Get real-time system metrics (Admin only)

**Response**:
```json
{
  "success": true,
  "data": {
    "systemHealth": "healthy",
    "uptime": "99.9%",
    "responseTime": 245,
    "activeConnections": 1234,
    "errorRate": 0.1
  }
}
```

## 🔍 Verification Endpoints

### GET /api/moderator/pending-reports
**Description**: Get reports pending verification (Moderator only)

**Query Parameters**:
- `priority`: Filter by priority
- `type`: Filter by disaster type
- `age`: Filter by report age (hours)

### GET /api/moderator/external-data/:reportId
**Description**: Get external data sources for report verification

**Response**:
```json
{
  "success": true,
  "data": {
    "usgsData": {...},
    "weatherData": {...},
    "newsArticles": [...],
    "governmentAlerts": [...]
  }
}
```

## 💬 Communication Endpoints

### GET /api/chat/conversations
**Description**: Get user's chat conversations

### POST /api/chat/messages
**Description**: Send a new message

**Request Body**:
```json
{
  "conversationId": "conv-123",
  "message": "Hello, I need assistance",
  "type": "text"
}
```

### GET /api/chat/messages/:conversationId
**Description**: Get messages for a conversation

## 🚨 Emergency Endpoints

### GET /api/alerts
**Description**: Get active disaster alerts

**Response**:
```json
{
  "success": true,
  "data": {
    "alerts": [
      {
        "id": "alert-123",
        "type": "earthquake",
        "severity": "high",
        "title": "Earthquake Alert",
        "description": "Strong earthquake detected",
        "affectedAreas": ["Yangon", "Mandalay"],
        "issuedAt": "2024-01-20T10:30:00Z",
        "instructions": ["Stay indoors", "Drop, cover, hold"]
      }
    ]
  }
}
```

### POST /api/alerts
**Description**: Create emergency alert (Admin only)

## 📤 File Upload Endpoints

### POST /api/upload/photos
**Description**: Upload report photos

**Request**: Multipart form data with image files

**Response**:
```json
{
  "success": true,
  "data": {
    "urls": [
      "https://storage.example.com/photo1.jpg",
      "https://storage.example.com/photo2.jpg"
    ]
  }
}
```

## ⚠️ Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

### Common Error Codes
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `VALIDATION_ERROR`: Invalid input data
- `NOT_FOUND`: Resource not found
- `RATE_LIMITED`: Too many requests
- `SERVER_ERROR`: Internal server error

## 🔒 Security Considerations

### Authentication
- JWT tokens for session management
- Token expiration and refresh mechanism
- Role-based access control (RBAC)

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Rate limiting

### Privacy
- GDPR compliance for user data
- Data anonymization options
- Secure data deletion
- Audit logging

## 📈 Rate Limiting

### Default Limits
- **General API**: 100 requests per minute per user
- **Report Creation**: 10 reports per hour per user
- **File Upload**: 50MB per hour per user
- **Admin Operations**: 1000 requests per minute

### Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## 🔄 Webhooks

### Available Events
- `report.created`: New report submitted
- `report.verified`: Report verification completed
- `user.suspended`: User account suspended
- `alert.issued`: Emergency alert issued

### Webhook Payload
```json
{
  "event": "report.created",
  "timestamp": "2024-01-20T10:30:00Z",
  "data": {
    "reportId": "report-123",
    "reporterId": "user-456",
    "priority": "high"
  }
}
```
