import React from 'react';
import { AlertTriangle, Heart, Users, MapPin, Clock, ChevronRight } from 'lucide-react';

interface DisasterReportCardProps {
  disasterType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  location: string;
  reportedTime: string;
  affectedPeople: number;
  resources: {
    medical: number; // percentage allocated
    foodWater: number;
    shelter: number;
  };
  emergencyContacts: string[];
}

const DisasterReportCard: React.FC<DisasterReportCardProps> = ({ 
  disasterType, 
  severity,
  location,
  reportedTime,
  affectedPeople,
  resources,
  emergencyContacts
}) => {
  const severityColors = {
    low: 'bg-green-100 text-green-800',
    medium: 'bg-yellow-100 text-yellow-800',
    high: 'bg-orange-100 text-orange-800',
    critical: 'bg-red-100 text-red-800'
  };

  const severityText = {
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical'
  };

  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl shadow-xl overflow-hidden border border-blue-100 transform transition-all hover:scale-[1.02] duration-300">
      <div className="p-8">
        {/* Header */}
        <div className="flex flex-wrap items-center justify-between mb-6 gap-4">
          <div className="flex items-center">
            <AlertTriangle size={24} className="text-red-500 mr-3" />
            <h3 className="text-2xl font-bold text-gray-900">{disasterType} Impact Report</h3>
          </div>
          <span className={`px-4 py-2 rounded-full text-sm font-semibold ${severityColors[severity]}`}>
            {severityText[severity]} Severity
          </span>
        </div>

        {/* Location and Time */}
        <div className="flex flex-wrap gap-6 mb-8">
          <div className="flex items-center">
            <MapPin size={18} className="text-blue-500 mr-2" />
            <span className="text-gray-700 font-medium">{location}</span>
          </div>
          <div className="flex items-center">
            <Clock size={18} className="text-purple-500 mr-2" />
            <span className="text-gray-700 font-medium">Reported: {reportedTime}</span>
          </div>
        </div>

        {/* Impact Statistics */}
        <div className="bg-white rounded-xl p-6 shadow-sm mb-8 border border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-gray-900 mb-2">{affectedPeople.toLocaleString()}</div>
              <div className="text-gray-600 flex items-center justify-center">
                <Users size={16} className="mr-1" /> Affected
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-4xl font-bold text-gray-900 mb-2">24</div>
              <div className="text-gray-600">Response Teams</div>
            </div>
            
            <div className="text-center">
              <div className="text-4xl font-bold text-gray-900 mb-2">87%</div>
              <div className="text-gray-600 flex items-center justify-center">
                <Heart size={16} className="mr-1" /> Needs Met
              </div>
            </div>
          </div>
        </div>

        {/* Resource Allocation */}
        <div className="mb-8">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Resource Allocation</h4>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-gray-700 font-medium">Medical Supplies</span>
                <span className="text-gray-700">{resources.medical}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-red-500 h-3 rounded-full" 
                  style={{ width: `${resources.medical}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-gray-700 font-medium">Food & Water</span>
                <span className="text-gray-700">{resources.foodWater}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-blue-500 h-3 rounded-full" 
                  style={{ width: `${resources.foodWater}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-gray-700 font-medium">Shelter</span>
                <span className="text-gray-700">{resources.shelter}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-green-500 h-3 rounded-full" 
                  style={{ width: `${resources.shelter}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Emergency Contacts */}
        <div>
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Emergency Contacts</h4>
          <div className="bg-blue-50 rounded-xl p-4">
            <ul className="space-y-2">
              {emergencyContacts.map((contact, index) => (
                <li key={index} className="flex items-center">
                  <ChevronRight size={16} className="text-blue-500 mr-2" />
                  <span className="text-blue-700 font-medium">{contact}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
      
      {/* Footer */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-4 text-center">
        <p className="text-white font-medium">
          Report updated in real-time • Last refresh: Just now
        </p>
      </div>
    </div>
  );
};

export default DisasterReportCard;
