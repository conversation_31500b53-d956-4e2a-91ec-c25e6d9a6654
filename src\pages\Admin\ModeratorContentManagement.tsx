import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Eye, 
  EyeOff, 
  Flag, 
  MessageSquare, 
  Clock, 
  CheckCircle,
  AlertTriangle,
  MoreVertical,
  Download,
  Trash2,
  Edit,
  MapPin,
  Calendar,
  User,
  Activity,
  TrendingUp,
  Shield
} from 'lucide-react';
import { format } from 'date-fns';
import { Report, ContentFlag, AssistanceInteraction } from '../../types';

interface ContentStats {
  totalReports: number;
  verifiedReports: number;
  hiddenReports: number;
  flaggedReports: number;
  assistanceInteractions: number;
  averageResponseTime: number;
}

const ModeratorContentManagement: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [showFlagModal, setShowFlagModal] = useState(false);
  const [selectedReportForFlag, setSelectedReportForFlag] = useState<string | null>(null);
  const [flagReason, setFlagReason] = useState('');
  const [flagType, setFlagType] = useState('inappropriate');
  const [stats, setStats] = useState<ContentStats>({
    totalReports: 0,
    verifiedReports: 0,
    hiddenReports: 0,
    flaggedReports: 0,
    assistanceInteractions: 0,
    averageResponseTime: 24
  });

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockReports: Report[] = [
      {
        id: '1',
        title: 'Flooding in Downtown District',
        disasterType: 'Natural',
        disasterDetail: 'Flash Flood',
        description: 'Severe flooding has affected multiple residential areas after heavy rainfall.',
        location: {
          lat: 40.7589,
          lng: -73.9851,
          address: 'Manhattan, New York, NY'
        },
        photos: [
          {
            id: '1',
            url: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=800',
            thumbnail: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=200',
            metadata: {
              size: 2048000,
              format: 'jpeg',
              uploadedAt: new Date()
            }
          }
        ],
        assistanceNeeded: ['Emergency Shelter', 'Food & Water'],
        assistanceDescription: 'Need temporary shelter for 15 families.',
        status: 'verified',
        priority: 'high',
        reporterId: '2',
        reporterName: 'Sarah Johnson',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15'),
        assistanceLog: [
          {
            id: '1',
            providerId: '1',
            providerName: 'John Anderson',
            description: 'Provided emergency shelter coordination.',
            createdAt: new Date('2024-01-16'),
            endorsed: true,
            type: 'offer',
            status: 'completed'
          }
        ],
        verificationData: {
          verifiedBy: 'moderator-1',
          verifiedAt: new Date('2024-01-15'),
          confidenceScore: 95
        },
        moderationData: {
          flagged: false,
          hidden: false
        }
      },
      {
        id: '2',
        title: 'Wildfire Damage Assessment',
        disasterType: 'Natural',
        disasterDetail: 'Wildfire',
        description: 'Fast-moving wildfire has damaged several residential properties.',
        location: {
          lat: 34.0522,
          lng: -118.2437,
          address: 'Los Angeles, CA'
        },
        photos: [
          {
            id: '2',
            url: 'https://images.pexels.com/photos/266487/pexels-photo-266487.jpeg?auto=compress&cs=tinysrgb&w=800',
            thumbnail: 'https://images.pexels.com/photos/266487/pexels-photo-266487.jpeg?auto=compress&cs=tinysrgb&w=200',
            metadata: {
              size: 1856000,
              format: 'jpeg',
              uploadedAt: new Date()
            }
          }
        ],
        assistanceNeeded: ['Evacuation Support', 'Pet Care'],
        assistanceDescription: 'Need evacuation assistance for elderly residents.',
        status: 'verified',
        priority: 'critical',
        reporterId: '3',
        reporterName: 'Mike Davis',
        createdAt: new Date('2024-01-12'),
        updatedAt: new Date('2024-01-12'),
        assistanceLog: [],
        verificationData: {
          verifiedBy: 'moderator-2',
          verifiedAt: new Date('2024-01-12'),
          confidenceScore: 88
        },
        moderationData: {
          flagged: true,
          flagReason: 'Duplicate report',
          flaggedBy: 'moderator-1',
          flaggedAt: new Date('2024-01-13'),
          hidden: false
        }
      },
      {
        id: '3',
        title: 'Tornado Aftermath Cleanup',
        disasterType: 'Natural',
        disasterDetail: 'Tornado',
        description: 'EF2 tornado caused significant damage to residential area.',
        location: {
          lat: 39.7392,
          lng: -104.9903,
          address: 'Denver, CO'
        },
        photos: [
          {
            id: '3',
            url: 'https://images.pexels.com/photos/1118869/pexels-photo-1118869.jpeg?auto=compress&cs=tinysrgb&w=800',
            thumbnail: 'https://images.pexels.com/photos/1118869/pexels-photo-1118869.jpeg?auto=compress&cs=tinysrgb&w=200',
            metadata: {
              size: 1756000,
              format: 'jpeg',
              uploadedAt: new Date()
            }
          }
        ],
        assistanceNeeded: ['Cleanup Volunteers', 'Construction Help'],
        assistanceDescription: 'Looking for volunteers to help with debris removal.',
        status: 'verified',
        priority: 'medium',
        reporterId: '4',
        reporterName: 'Lisa Wilson',
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-10'),
        assistanceLog: [],
        verificationData: {
          verifiedBy: 'moderator-1',
          verifiedAt: new Date('2024-01-10'),
          confidenceScore: 92
        },
        moderationData: {
          flagged: false,
          hidden: true,
          hiddenBy: 'moderator-2',
          hiddenAt: new Date('2024-01-11'),
          hiddenReason: 'Inappropriate content in comments'
        }
      }
    ];

    setReports(mockReports);
    setStats({
      totalReports: mockReports.length,
      verifiedReports: mockReports.filter(r => r.status === 'verified').length,
      hiddenReports: mockReports.filter(r => r.moderationData?.hidden).length,
      flaggedReports: mockReports.filter(r => r.moderationData?.flagged).length,
      assistanceInteractions: mockReports.reduce((acc, r) => acc + r.assistanceLog.length, 0),
      averageResponseTime: 24
    });
  }, []);

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.reporterName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'verified' && report.status === 'verified') ||
                         (statusFilter === 'flagged' && report.moderationData?.flagged) ||
                         (statusFilter === 'hidden' && report.moderationData?.hidden);
    
    const matchesType = typeFilter === 'all' || report.disasterType === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const handleSelectReport = (reportId: string) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId)
        : [...prev, reportId]
    );
  };

  const handleSelectAll = () => {
    if (selectedReports.length === filteredReports.length) {
      setSelectedReports([]);
    } else {
      setSelectedReports(filteredReports.map(report => report.id));
    }
  };

  const handleToggleVisibility = (reportId: string) => {
    setReports(prev => prev.map(report => 
      report.id === reportId 
        ? {
            ...report,
            moderationData: {
              ...report.moderationData,
              hidden: !report.moderationData?.hidden,
              hiddenBy: !report.moderationData?.hidden ? 'current-moderator' : undefined,
              hiddenAt: !report.moderationData?.hidden ? new Date() : undefined,
              hiddenReason: !report.moderationData?.hidden ? 'Moderator action' : undefined
            }
          }
        : report
    ));
  };

  const handleFlagReport = () => {
    if (!selectedReportForFlag) return;

    setReports(prev => prev.map(report => 
      report.id === selectedReportForFlag 
        ? {
            ...report,
            moderationData: {
              ...report.moderationData,
              flagged: true,
              flagReason: flagReason,
              flaggedBy: 'current-moderator',
              flaggedAt: new Date()
            }
          }
        : report
    ));

    setShowFlagModal(false);
    setSelectedReportForFlag(null);
    setFlagReason('');
  };

  const handleBulkAction = (action: string) => {
    switch (action) {
      case 'hide':
        setReports(prev => prev.map(report => 
          selectedReports.includes(report.id)
            ? {
                ...report,
                moderationData: {
                  ...report.moderationData,
                  hidden: true,
                  hiddenBy: 'current-moderator',
                  hiddenAt: new Date(),
                  hiddenReason: 'Bulk moderation action'
                }
              }
            : report
        ));
        break;
      case 'unhide':
        setReports(prev => prev.map(report => 
          selectedReports.includes(report.id)
            ? {
                ...report,
                moderationData: {
                  ...report.moderationData,
                  hidden: false,
                  hiddenBy: undefined,
                  hiddenAt: undefined,
                  hiddenReason: undefined
                }
              }
            : report
        ));
        break;
      case 'export':
        // Handle export functionality
        console.log('Exporting reports:', selectedReports);
        break;
    }
    setSelectedReports([]);
  };

  const getStatusBadge = (report: Report) => {
    if (report.moderationData?.hidden) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">Hidden</span>;
    }
    if (report.moderationData?.flagged) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">Flagged</span>;
    }
    if (report.status === 'verified') {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">Verified</span>;
    }
    return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">Pending</span>;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Content Management</h1>
          <p className="text-gray-600">Manage verified reports, flags, and assistance interactions</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Reports</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalReports}</p>
            </div>
            <div className="p-3 bg-blue-100 text-blue-600 rounded-xl">
              <Activity size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Verified</p>
              <p className="text-2xl font-bold text-gray-900">{stats.verifiedReports}</p>
            </div>
            <div className="p-3 bg-green-100 text-green-600 rounded-xl">
              <CheckCircle size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Hidden</p>
              <p className="text-2xl font-bold text-gray-900">{stats.hiddenReports}</p>
            </div>
            <div className="p-3 bg-gray-100 text-gray-600 rounded-xl">
              <EyeOff size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Flagged</p>
              <p className="text-2xl font-bold text-gray-900">{stats.flaggedReports}</p>
            </div>
            <div className="p-3 bg-red-100 text-red-600 rounded-xl">
              <Flag size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Interactions</p>
              <p className="text-2xl font-bold text-gray-900">{stats.assistanceInteractions}</p>
            </div>
            <div className="p-3 bg-purple-100 text-purple-600 rounded-xl">
              <MessageSquare size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Response</p>
              <p className="text-2xl font-bold text-gray-900">{stats.averageResponseTime}h</p>
            </div>
            <div className="p-3 bg-orange-100 text-orange-600 rounded-xl">
              <Clock size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search reports..."
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="relative">
            <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              <option value="all">All Status</option>
              <option value="verified">Verified</option>
              <option value="flagged">Flagged</option>
              <option value="hidden">Hidden</option>
            </select>
          </div>
          <div className="relative">
            <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              <option value="all">All Types</option>
              <option value="Natural">Natural Disasters</option>
              <option value="Non-Natural">Non-Natural Disasters</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedReports.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-900">
              {selectedReports.length} report{selectedReports.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => handleBulkAction('hide')}
                className="px-3 py-1 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
              >
                Hide
              </button>
              <button 
                onClick={() => handleBulkAction('unhide')}
                className="px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
              >
                Unhide
              </button>
              <button 
                onClick={() => handleBulkAction('export')}
                className="px-3 py-1 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reports Table */}
      <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="w-12 px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedReports.length === filteredReports.length && filteredReports.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Report</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Status</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Priority</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Reporter</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Assistance</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Created</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredReports.map((report) => (
                <tr key={report.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedReports.includes(report.id)}
                      onChange={() => handleSelectReport(report.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-start space-x-3">
                      {report.photos.length > 0 && (
                        <img
                          src={report.photos[0].thumbnail || report.photos[0].url}
                          alt=""
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate">{report.title}</p>
                        <p className="text-sm text-gray-600 truncate">{report.disasterDetail}</p>
                        <div className="flex items-center space-x-1 mt-1">
                          <MapPin size={12} className="text-gray-400" />
                          <span className="text-xs text-gray-500 truncate">{report.location.address}</span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    {getStatusBadge(report)}
                  </td>
                  <td className="px-6 py-4">
                    <span className={`font-medium ${getPriorityColor(report.priority)}`}>
                      {report.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <User size={16} className="text-gray-400" />
                      <span className="text-sm text-gray-900">{report.reporterName}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <MessageSquare size={16} className="text-gray-400" />
                      <span className="text-sm text-gray-900">{report.assistanceLog.length}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {format(report.createdAt, 'MMM d, yyyy')}
                      <p className="text-gray-600">{format(report.createdAt, 'HH:mm')}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleToggleVisibility(report.id)}
                        className={`p-2 rounded-lg transition-colors ${
                          report.moderationData?.hidden
                            ? 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                            : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'
                        }`}
                        title={report.moderationData?.hidden ? 'Unhide report' : 'Hide report'}
                      >
                        {report.moderationData?.hidden ? <Eye size={16} /> : <EyeOff size={16} />}
                      </button>
                      <button
                        onClick={() => {
                          setSelectedReportForFlag(report.id);
                          setShowFlagModal(true);
                        }}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Flag report"
                      >
                        <Flag size={16} />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                        <MoreVertical size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Flag Modal */}
      {showFlagModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Flag Report</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Flag Type</label>
                <select
                  value={flagType}
                  onChange={(e) => setFlagType(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="inappropriate">Inappropriate Content</option>
                  <option value="spam">Spam</option>
                  <option value="duplicate">Duplicate Report</option>
                  <option value="misinformation">Misinformation</option>
                  <option value="harassment">Harassment</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                <textarea
                  value={flagReason}
                  onChange={(e) => setFlagReason(e.target.value)}
                  placeholder="Provide details about why this report is being flagged..."
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={4}
                />
              </div>
              <div className="flex space-x-3 pt-4">
                <button
                  onClick={handleFlagReport}
                  className="flex-1 bg-red-600 text-white py-3 rounded-xl hover:bg-red-700 transition-colors"
                >
                  Flag Report
                </button>
                <button
                  onClick={() => {
                    setShowFlagModal(false);
                    setSelectedReportForFlag(null);
                    setFlagReason('');
                  }}
                  className="flex-1 border border-gray-300 text-gray-700 py-3 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModeratorContentManagement;
