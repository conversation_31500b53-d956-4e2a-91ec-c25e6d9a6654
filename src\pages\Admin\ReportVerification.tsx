import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft,
  MapPin,
  Calendar,
  User,
  Camera,
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Globe,
  Clock,
  Shield,
  Eye,
  ZoomIn,
  Download,
  Flag,
  MessageSquare
} from 'lucide-react';
import { format } from 'date-fns';
import { Report, ReportVerification as ReportVerificationType, ExternalDataSource } from '../../types';

const ReportVerification: React.FC = () => {
  const { reportId } = useParams<{ reportId: string }>();
  const navigate = useNavigate();
  const [report, setReport] = useState<Report | null>(null);
  const [verification, setVerification] = useState<Partial<ReportVerificationType>>({
    reasonCodes: [],
    evidence: [],
    externalDataChecked: [],
    confidence: 50,
    notes: ''
  });
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);
  const [externalData, setExternalData] = useState<ExternalDataSource[]>([]);
  const [isVerifying, setIsVerifying] = useState(false);

  // Mock data - in real app, this would come from API
  useEffect(() => {
    if (reportId) {
      const mockReport: Report = {
        id: reportId,
        title: 'Severe Flooding in Downtown Area',
        disasterType: 'Natural',
        disasterDetail: 'Flash Flood',
        description: 'Heavy rainfall has caused severe flooding in the downtown business district. Water levels are approximately 2-3 feet deep on Main Street. Multiple businesses are affected and some residents are trapped in upper floors.',
        location: {
          lat: 40.7589,
          lng: -73.9851,
          address: 'Downtown Manhattan, New York, NY',
          accuracy: 95
        },
        photos: [
          {
            id: '1',
            url: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=800',
            thumbnail: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=200',
            caption: 'Main Street flooding - water level approximately 3 feet',
            metadata: {
              size: 2048000,
              format: 'jpeg',
              dimensions: { width: 1920, height: 1080 },
              uploadedAt: new Date()
            }
          },
          {
            id: '2',
            url: 'https://images.pexels.com/photos/1592119/pexels-photo-1592119.jpeg?auto=compress&cs=tinysrgb&w=800',
            thumbnail: 'https://images.pexels.com/photos/1592119/pexels-photo-1592119.jpeg?auto=compress&cs=tinysrgb&w=200',
            caption: 'Flooded intersection near City Hall',
            metadata: {
              size: 1856000,
              format: 'jpeg',
              dimensions: { width: 1600, height: 900 },
              uploadedAt: new Date()
            }
          }
        ],
        assistanceNeeded: ['Emergency Shelter', 'Food & Water', 'Medical Aid', 'Transportation'],
        assistanceDescription: 'Immediate need for temporary shelter for affected families. Clean drinking water and medical supplies urgently required. Transportation needed for elderly residents.',
        status: 'pending',
        priority: 'high',
        reporterId: '2',
        reporterName: 'Sarah Johnson',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        assistanceLog: [],
        metadata: {
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          submissionMethod: 'web'
        }
      };

      const mockExternalData: ExternalDataSource[] = [
        {
          id: '1',
          name: 'USGS Water Data',
          type: 'usgs',
          url: 'https://waterdata.usgs.gov/monitoring-location/01311500/',
          lastChecked: new Date(Date.now() - 30 * 60 * 1000),
          status: 'verified',
          data: {
            waterLevel: '15.2 ft',
            floodStage: '12.0 ft',
            status: 'Above flood stage'
          }
        },
        {
          id: '2',
          name: 'National Weather Service',
          type: 'weather',
          url: 'https://forecast.weather.gov/MapClick.php?lat=40.7589&lon=-73.9851',
          lastChecked: new Date(Date.now() - 15 * 60 * 1000),
          status: 'verified',
          data: {
            rainfall: '3.2 inches in 2 hours',
            floodWarning: 'Active until 8:00 PM',
            severity: 'Moderate'
          }
        },
        {
          id: '3',
          name: 'NYC Emergency Management',
          type: 'government',
          url: 'https://www1.nyc.gov/site/em/index.page',
          lastChecked: new Date(Date.now() - 10 * 60 * 1000),
          status: 'verified',
          data: {
            alertLevel: 'Yellow',
            affectedAreas: ['Manhattan Downtown', 'Financial District'],
            evacuationStatus: 'Voluntary'
          }
        }
      ];

      setReport(mockReport);
      setExternalData(mockExternalData);
    }
  }, [reportId]);

  const handleVerificationSubmit = async (status: 'verified' | 'fake') => {
    setIsVerifying(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const verificationData: ReportVerificationType = {
      id: `verification-${Date.now()}`,
      reportId: reportId!,
      verifiedBy: 'current-moderator-id',
      status,
      verificationDate: new Date(),
      reasonCodes: verification.reasonCodes || [],
      evidence: verification.evidence || [],
      externalDataChecked: externalData,
      confidence: verification.confidence || 50,
      notes: verification.notes || '',
      reviewHistory: []
    };

    console.log('Verification submitted:', verificationData);
    setIsVerifying(false);
    navigate('/admin/moderator');
  };

  const addReasonCode = (code: string) => {
    setVerification(prev => ({
      ...prev,
      reasonCodes: [...(prev.reasonCodes || []), code]
    }));
  };

  const removeReasonCode = (code: string) => {
    setVerification(prev => ({
      ...prev,
      reasonCodes: (prev.reasonCodes || []).filter(c => c !== code)
    }));
  };

  const reasonCodeOptions = [
    'Photo evidence supports claim',
    'Location verified via external data',
    'Timeline consistent with weather data',
    'Reporter has good history',
    'Multiple corroborating sources',
    'Government alerts confirm event',
    'USGS data supports flooding',
    'Weather service confirms conditions',
    'Photo metadata appears authentic',
    'Location accuracy high'
  ];

  const fakeReasonCodes = [
    'Photo appears manipulated',
    'Location inconsistent with claim',
    'Timeline doesn\'t match weather data',
    'Reporter has suspicious history',
    'No external data supports claim',
    'Photo metadata suspicious',
    'Duplicate of existing report',
    'Inconsistent details',
    'No government alerts for area',
    'Weather data contradicts claim'
  ];

  if (!report) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/admin/moderator')}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Report Verification</h1>
            <p className="text-gray-600">Review and verify disaster report #{report.id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${
            report.priority === 'critical' ? 'bg-red-100 text-red-800 border-red-200' :
            report.priority === 'high' ? 'bg-orange-100 text-orange-800 border-orange-200' :
            report.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
            'bg-green-100 text-green-800 border-green-200'
          }`}>
            {report.priority} priority
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Report Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Report Details</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{report.title}</h3>
                <p className="text-gray-600">{report.description}</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <AlertTriangle size={16} />
                  <span>{report.disasterType} - {report.disasterDetail}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Calendar size={16} />
                  <span>{format(report.createdAt, 'MMM d, yyyy HH:mm')}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <User size={16} />
                  <span>{report.reporterName}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin size={16} />
                  <span>{report.location.address}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Photos */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Photo Evidence</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {report.photos.map((photo) => (
                <div key={photo.id} className="relative group">
                  <img
                    src={photo.url}
                    alt={photo.caption}
                    className="w-full h-48 object-cover rounded-xl cursor-pointer"
                    onClick={() => setSelectedPhoto(photo.url)}
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors rounded-xl flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                      <button className="p-2 bg-white/90 text-gray-700 rounded-lg hover:bg-white transition-colors">
                        <ZoomIn size={16} />
                      </button>
                      <button className="p-2 bg-white/90 text-gray-700 rounded-lg hover:bg-white transition-colors">
                        <Download size={16} />
                      </button>
                    </div>
                  </div>
                  {photo.caption && (
                    <p className="text-sm text-gray-600 mt-2">{photo.caption}</p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* External Data Verification */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">External Data Sources</h2>
            <div className="space-y-4">
              {externalData.map((source) => (
                <div key={source.id} className="border border-gray-200 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <Globe size={20} className="text-blue-600" />
                      <h3 className="font-medium text-gray-900">{source.name}</h3>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        source.status === 'verified' ? 'bg-green-100 text-green-800' :
                        source.status === 'no_data' ? 'bg-yellow-100 text-yellow-800' :
                        source.status === 'contradicts' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {source.status}
                      </span>
                    </div>
                    <a
                      href={source.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-700"
                    >
                      <ExternalLink size={16} />
                    </a>
                  </div>
                  {source.data && (
                    <div className="bg-gray-50 rounded-lg p-3">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(source.data, null, 2)}
                      </pre>
                    </div>
                  )}
                  <p className="text-xs text-gray-500 mt-2">
                    Last checked: {format(source.lastChecked, 'MMM d, HH:mm')}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Verification Panel */}
        <div className="space-y-6">
          {/* Verification Actions */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Verification Decision</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => handleVerificationSubmit('verified')}
                  disabled={isVerifying}
                  className="flex items-center justify-center space-x-2 bg-green-600 text-white py-3 px-4 rounded-xl hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  <CheckCircle size={16} />
                  <span>Verify</span>
                </button>
                <button
                  onClick={() => handleVerificationSubmit('fake')}
                  disabled={isVerifying}
                  className="flex items-center justify-center space-x-2 bg-red-600 text-white py-3 px-4 rounded-xl hover:bg-red-700 transition-colors disabled:opacity-50"
                >
                  <XCircle size={16} />
                  <span>Mark Fake</span>
                </button>
              </div>
              
              {/* Confidence Slider */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Confidence Level: {verification.confidence}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={verification.confidence}
                  onChange={(e) => setVerification(prev => ({ ...prev, confidence: parseInt(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Verification Notes
                </label>
                <textarea
                  value={verification.notes}
                  onChange={(e) => setVerification(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Add detailed notes about your verification decision..."
                  className="w-full px-3 py-2 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={4}
                />
              </div>
            </div>
          </div>

          {/* Reason Codes */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Reason Codes</h2>
            <div className="space-y-3">
              <div>
                <h3 className="text-sm font-medium text-green-700 mb-2">Supporting Evidence</h3>
                <div className="space-y-1">
                  {reasonCodeOptions.map((code) => (
                    <label key={code} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={verification.reasonCodes?.includes(code) || false}
                        onChange={(e) => {
                          if (e.target.checked) {
                            addReasonCode(code);
                          } else {
                            removeReasonCode(code);
                          }
                        }}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="text-gray-700">{code}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-red-700 mb-2">Contradicting Evidence</h3>
                <div className="space-y-1">
                  {fakeReasonCodes.map((code) => (
                    <label key={code} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={verification.reasonCodes?.includes(code) || false}
                        onChange={(e) => {
                          if (e.target.checked) {
                            addReasonCode(code);
                          } else {
                            removeReasonCode(code);
                          }
                        }}
                        className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                      />
                      <span className="text-gray-700">{code}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Reporter Information */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Reporter Profile</h2>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <User size={20} className="text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{report.reporterName}</p>
                  <p className="text-sm text-gray-600">Reporter ID: {report.reporterId}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Reports Submitted:</span>
                  <span className="font-medium text-gray-900 ml-1">12</span>
                </div>
                <div>
                  <span className="text-gray-600">Verified:</span>
                  <span className="font-medium text-green-600 ml-1">10</span>
                </div>
                <div>
                  <span className="text-gray-600">Trust Score:</span>
                  <span className="font-medium text-blue-600 ml-1">85%</span>
                </div>
                <div>
                  <span className="text-gray-600">Member Since:</span>
                  <span className="font-medium text-gray-900 ml-1">Jan 2023</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Photo Modal */}
      {selectedPhoto && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <img
              src={selectedPhoto}
              alt="Full size"
              className="max-w-full max-h-full object-contain rounded-xl"
            />
            <button
              onClick={() => setSelectedPhoto(null)}
              className="absolute top-4 right-4 p-2 bg-white/90 text-gray-700 rounded-lg hover:bg-white transition-colors"
            >
              <XCircle size={20} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportVerification;
