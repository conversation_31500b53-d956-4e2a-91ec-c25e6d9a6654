# DisasterWatch - Disaster Reporting Platform

## 🌟 Overview

DisasterWatch is a comprehensive disaster reporting and management platform built with React and TypeScript. It enables communities to report disasters, coordinate assistance, and manage emergency responses through a unified system with role-based access control.

## 🚀 Features

### Core Features
- **Disaster Reporting**: Multi-step form for reporting natural and non-natural disasters
- **Real-time Verification**: Moderator workflow for report verification with external data integration
- **Interactive Maps**: Location-based reporting and visualization using Leaflet
- **Role-based Access Control**: Admin, Moderator, Editor, User, Volunteer, and Organization roles
- **Content Management**: Comprehensive content moderation and management tools
- **Analytics Dashboard**: Real-time metrics and reporting analytics
- **Chat System**: Built-in communication system for coordination
- **Mobile Responsive**: Optimized for all device sizes

### User Roles & Permissions

#### 🔴 Admin
- Full system access and configuration
- User management and role assignment
- System analytics and reporting
- Content publishing and scheduling
- Security and access control management

#### 🟡 Moderator
- Report verification and validation
- Content flagging and moderation
- User suspension and violation management
- External data source integration

#### 🟢 Editor
- Content creation and editing
- Report management
- Limited user interaction tools

#### 🔵 User/Volunteer/Organization
- Disaster report creation
- Assistance coordination
- Community interaction
- Personal dashboard access

## 🏗️ Architecture

### Frontend Stack
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **React Router** for navigation
- **Leaflet** for interactive maps
- **Recharts** for data visualization
- **Lucide React** for icons
- **date-fns** for date handling

### Project Structure
```
src/
├── components/           # Reusable UI components
│   ├── Admin/           # Admin-specific components
│   ├── Auth/            # Authentication components
│   ├── Chat/            # Chat system components
│   ├── Common/          # Shared components
│   ├── Layout/          # Layout components
│   └── Map/             # Map-related components
├── context/             # React Context providers
│   ├── AuthContext.tsx  # Authentication state management
│   └── AdminContext.tsx # Admin-specific state management
├── data/                # Mock data and constants
├── pages/               # Page components
│   ├── Admin/           # Admin dashboard pages
│   └── [other pages]    # Public and user pages
├── types/               # TypeScript type definitions
│   ├── index.ts         # Core types
│   └── admin.ts         # Admin-specific types
└── App.tsx              # Main application component
```

## 🔧 Installation & Setup

### Prerequisites
- Node.js 16+ 
- npm or yarn package manager

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd disaster-reporting-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🔐 Authentication System

### Mock Users for Testing
The platform includes pre-configured mock users for testing different roles:

#### Admin User
- **Email**: <EMAIL>
- **Password**: admin123
- **Access**: Full system administration

#### Moderator User  
- **Email**: <EMAIL>
- **Password**: mod123
- **Access**: Report verification and content moderation

#### Editor User
- **Email**: <EMAIL>  
- **Password**: editor123
- **Access**: Content management and editing

#### Regular User
- **Email**: <EMAIL>
- **Password**: user123
- **Access**: Report creation and personal dashboard

### Authentication Features
- Session-based authentication with localStorage
- Role-based route protection
- Permission-based component access
- Automatic role-based redirects
- Secure logout functionality

## 📊 Key Components

### Report Management
- **CreateReport**: Multi-step disaster reporting form
- **ReportDetail**: Detailed report view with assistance tracking
- **Reports**: Report listing with filtering and search

### Admin Dashboard
- **AdminDashboard**: System overview with analytics
- **UserManagement**: User administration tools
- **ContentManagement**: Content publishing system
- **SystemReport**: System health and performance metrics

### Moderator Workflow
- **ModeratorDashboard**: Pending reports queue with statistics
- **ReportVerification**: Detailed verification interface with external data
- **ModeratorContentManagement**: Content flagging and moderation
- **ModeratorUserManagement**: User suspension and violation tracking

### Maps & Location
- **LocationPicker**: Interactive map for location selection
- **MapView**: Report visualization on interactive maps

### Communication
- **ChatWidget**: Real-time chat system
- **NotificationDemo**: Notification system demonstration

## 🎨 UI/UX Features

### Design System
- **Modern Interface**: Clean, professional design with rounded corners
- **Color Coding**: Intuitive color system for different states and priorities
- **Responsive Layout**: Mobile-first design approach
- **Accessibility**: WCAG compliant components
- **Dark Mode Ready**: Prepared for dark theme implementation

### Interactive Elements
- **Smooth Animations**: Subtle transitions and hover effects
- **Loading States**: Comprehensive loading indicators
- **Error Handling**: User-friendly error messages
- **Form Validation**: Real-time form validation feedback

## 📱 Mobile Optimization

- Responsive grid layouts
- Touch-friendly interface elements
- Optimized map interactions
- Mobile-specific navigation patterns
- Compressed image handling

## 🔒 Security Features

- Role-based access control (RBAC)
- Protected routes with permission checking
- Secure session management
- Input validation and sanitization
- XSS protection measures

## 🌐 External Integrations

### Planned Integrations
- **USGS Earthquake API**: Real-time earthquake data
- **Weather Services**: Weather condition verification
- **Government Alerts**: Official emergency notifications
- **News APIs**: Related news article integration
- **Social Media**: Social media monitoring and verification

## 📈 Analytics & Reporting

### Metrics Tracked
- Report submission rates
- Verification times and accuracy
- User engagement metrics
- System performance indicators
- Geographic distribution of reports

### Dashboard Features
- Real-time statistics
- Interactive charts and graphs
- Exportable reports
- Trend analysis
- Performance monitoring

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
Create a `.env` file for environment-specific configurations:
```env
VITE_API_BASE_URL=your_api_url
VITE_MAP_API_KEY=your_map_api_key
VITE_ANALYTICS_ID=your_analytics_id
```

### Deployment Platforms
- **Vercel**: Recommended for React applications
- **Netlify**: Alternative static hosting
- **AWS S3 + CloudFront**: Enterprise deployment
- **Docker**: Containerized deployment option

## 🤝 Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Use Tailwind CSS for styling
3. Implement proper error handling
4. Write comprehensive tests
5. Follow component composition patterns

### Code Style
- Use functional components with hooks
- Implement proper TypeScript typing
- Follow naming conventions
- Use ESLint and Prettier for code formatting

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation wiki

---

**DisasterWatch** - Uniting Communities in Times of Crisis 🌍
