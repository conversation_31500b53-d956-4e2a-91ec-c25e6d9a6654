import React from 'react';
import { Server, Database, Cpu, HardDrive, Network, CheckCircle, XCircle, AlertCircle, Clock } from 'lucide-react';

interface SystemStatusProps {
  status: 'operational' | 'degraded' | 'outage';
  services: {
    name: string;
    status: 'up' | 'down' | 'warning';
    responseTime: number;
  }[];
}

const SystemStatus: React.FC<SystemStatusProps> = ({ status, services }) => {
  const statusColors = {
    operational: 'bg-green-100 text-green-800',
    degraded: 'bg-yellow-100 text-yellow-800',
    outage: 'bg-red-100 text-red-800'
  };

  const statusText = {
    operational: 'All Systems Operational',
    degraded: 'Partial Outage',
    outage: 'Major Outage'
  };

  const statusIcons = {
    up: <CheckCircle size={16} className="text-green-500" />,
    warning: <AlertCircle size={16} className="text-yellow-500" />,
    down: <XCircle size={16} className="text-red-500" />
  };

  return (
    <div className="bg-white rounded-3xl shadow-sm border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className={`p-6 ${statusColors[status]} flex items-center justify-between`}>
        <div className="flex items-center">
          <Server size={24} className="mr-3" />
          <h3 className="text-xl font-bold">System Status</h3>
        </div>
        <span className="text-lg font-semibold">{statusText[status]}</span>
      </div>

      {/* Services */}
      <div className="p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Service Status</h4>
        <div className="space-y-4">
          {services.map((service, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                {service.name === 'API Server' && <Cpu size={18} className="text-blue-500 mr-3" />}
                {service.name === 'Database' && <Database size={18} className="text-purple-500 mr-3" />}
                {service.name === 'File Storage' && <HardDrive size={18} className="text-green-500 mr-3" />}
                {service.name === 'Network' && <Network size={18} className="text-red-500 mr-3" />}
                <span className="font-medium text-gray-700">{service.name}</span>
              </div>
              <div className="flex items-center">
                {statusIcons[service.status]}
                <span className="ml-2 text-gray-500 text-sm">{service.responseTime}ms</span>
              </div>
            </div>
          ))}
        </div>

        {/* Uptime */}
        <div className="mt-8 pt-6 border-t border-gray-100">
          <div className="flex items-center">
            <Clock size={18} className="text-gray-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Current Uptime</p>
              <p className="font-bold text-lg text-gray-900">99.98%</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemStatus;
