import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>gle, Users, CheckCircle, MapPin, Calendar, Eye, ArrowRight, Shield, Heart, Clock, Star, ChevronRight, Award, Globe, Zap, X, Play, CheckSquare, Camera, MessageCircle, TrendingUp, BarChart3, Activity } from 'lucide-react';
import { mockReports } from '../data/mockData';
import { format } from 'date-fns';
import ChatWidget from '../components/Chat/ChatWidget';
import ViewReportsButton, { ViewReportsCard } from '../components/Common/ViewReportsButton';
import DisasterReportCard from '../components/Common/DisasterReportCard';

const Home: React.FC = () => {
  const recentReports = mockReports.slice(0, 3);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Emergency Coordinator",
      location: "San Francisco, CA",
      avatar: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1",
      content: "DisasterWatch helped us coordinate relief efforts during the recent flooding. The real-time reporting system was invaluable for our response team.",
      rating: 5
    },
    {
      id: 2,
      name: "Michael Rodriguez",
      role: "Community Volunteer",
      location: "Austin, TX",
      avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1",
      content: "I've been able to help 12 families through this platform. It's amazing how technology can bring communities together during tough times.",
      rating: 5
    },
    {
      id: 3,
      name: "Dr. Emily Watson",
      role: "Disaster Response Specialist",
      location: "Miami, FL",
      avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1",
      content: "The verification system ensures accurate information reaches the right people. This platform has revolutionized how we handle disaster response.",
      rating: 5
    }
  ];

  const partners = [
    { name: "American Red Cross", logo: "https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "FEMA", logo: "https://images.pexels.com/photos/8815176/pexels-photo-8815176.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "Salvation Army", logo: "https://images.pexels.com/photos/3862627/pexels-photo-3862627.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "United Way", logo: "https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "Doctors Without Borders", logo: "https://images.pexels.com/photos/8815176/pexels-photo-8815176.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "World Health Organization", logo: "https://images.pexels.com/photos/3862627/pexels-photo-3862627.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" }
  ];

  const stats = [
    { label: "Reports Submitted", value: "2,847", icon: AlertTriangle, color: "text-red-600" },
    { label: "Lives Helped", value: "12,450", icon: Users, color: "text-blue-600" },
    { label: "Verified Reports", value: "2,189", icon: CheckCircle, color: "text-green-600" },
    { label: "Response Time", value: "< 2hrs", icon: Clock, color: "text-purple-600" }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm rounded-full text-blue-100 text-sm font-medium border border-white/20 mb-8">
              <Zap size={16} className="mr-2" />
              Trusted by 50,000+ community members worldwide
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold leading-tight mb-8">
              Unite Communities in
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-purple-300 to-indigo-300">
                Times of Crisis
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed mb-12 max-w-3xl mx-auto">
              Report disasters, offer assistance, and build resilience together. Every voice matters in community recovery.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Link
                to="/report/new"
                className="group bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all transform hover:-translate-y-1 hover:shadow-2xl flex items-center justify-center border border-blue-400/20"
              >
                Report an Impact
                <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
              
              <ViewReportsButton 
                variant="outline" 
                size="lg" 
                className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
              />
            </div>

            {/* Stats - Flexbox Layout */}
            <div className="flex flex-wrap justify-center gap-6 md:gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="flex-shrink-0 text-center min-w-[120px]">
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-blue-200 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="py-16 bg-gray-50 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <p className="text-gray-600 font-medium text-lg mb-4">Trusted by leading organizations worldwide</p>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
          </div>
          
          {/* Partners - Flexbox Layout */}
          <div className="flex flex-wrap justify-center items-center gap-6 md:gap-8">
            {partners.map((partner, index) => (
              <div key={index} className="flex flex-col items-center space-y-3 group flex-shrink-0 min-w-[120px]">
                <img
                  src={partner.logo}
                  alt={partner.name}
                  className="h-12 w-20 object-cover rounded-lg grayscale group-hover:grayscale-0 transition-all duration-300 shadow-sm"
                />
                <span className="text-gray-700 font-medium text-sm text-center">{partner.name}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Disaster Report */}
      <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Active Disaster Report
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Real-time updates on current disaster situations and response efforts
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <DisasterReportCard
              disasterType="Flood"
              severity="high"
              location="Yangon, Myanmar"
              reportedTime="July 3, 2025 5:30 AM"
              affectedPeople={12500}
              resources={{
                medical: 65,
                foodWater: 45,
                shelter: 75
              }}
              emergencyContacts={[
                "Myanmar Red Cross: +95 1 383 686",
                "National Disaster Management: 067-340-3663",
                "Emergency Rescue: 191"
              ]}
            />
          </div>
        </div>
      </section>

      {/* Recent Reports */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-16 gap-6">
            <div className="flex-1">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Recent Verified Reports
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl">
                Stay informed about recent disasters and community needs in your area
              </p>
            </div>
            <div className="flex-shrink-0">
              <ViewReportsButton 
                className="hidden md:flex"
                showIcon={false}
              />
            </div>
          </div>

          {/* Reports - Flexbox Layout */}
          <div className="flex flex-wrap gap-6 lg:gap-8 justify-center">
            {recentReports.map((report) => (
              <div key={report.id} className="flex-1 min-w-[300px] max-w-[400px] bg-white rounded-3xl shadow-sm hover:shadow-xl transition-all duration-500 overflow-hidden border border-gray-100 group">
                <div className="relative h-56 overflow-hidden">
                  <img
                    src={report.photos[0]}
                    alt={report.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute top-6 left-6">
                    <span className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                      {report.disasterDetail}
                    </span>
                  </div>
                  <div className="absolute top-6 right-6">
                    <div className="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-semibold text-gray-700 flex items-center space-x-1">
                      <CheckCircle size={12} className="text-green-500" />
                      <span>Verified</span>
                    </div>
                  </div>
                  <div className="absolute bottom-6 left-6 right-6">
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-200 transition-colors">
                      {report.title}
                    </h3>
                  </div>
                </div>
                
                <div className="p-8">
                  <p className="text-gray-600 mb-6 leading-relaxed line-clamp-3">
                    {report.description}
                  </p>
                  
                  <div className="flex flex-wrap items-center text-sm text-gray-500 mb-6 gap-4">
                    <div className="flex items-center space-x-2">
                      <MapPin size={16} className="text-blue-500" />
                      <span>{report.location.address.split(',')[0]}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-green-500" />
                      <span>{format(report.createdAt, 'MMM d')}</span>
                    </div>
                  </div>
                  
                  <Link
                    to={`/reports/${report.id}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold group/link"
                  >
                    <Eye size={16} className="mr-2" />
                    View Details
                    <ArrowRight size={14} className="ml-2 group-hover/link:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-16 md:hidden">
            <ViewReportsButton size="lg" />
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Trusted by Communities
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Nationwide
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how DisasterWatch is making a difference in disaster response and community resilience
            </p>
          </div>

          {/* Testimonials - Flexbox Layout */}
          <div className="flex flex-wrap gap-6 lg:gap-8 justify-center">
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="flex-1 min-w-[300px] max-w-[400px] bg-gray-50 rounded-3xl p-8 border border-gray-100 hover:shadow-xl transition-all duration-500 group">
                <div className="flex items-center mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} size={18} className="text-yellow-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-gray-700 mb-8 leading-relaxed text-lg">
                  "{testimonial.content}"
                </blockquote>
                <div className="flex items-center">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-14 h-14 rounded-full object-cover mr-4 ring-4 ring-blue-100"
                  />
                  <div>
                    <div className="font-semibold text-gray-900 text-lg">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role}</div>
                    <div className="text-xs text-gray-500">{testimonial.location}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-8">
            Ready to Make a
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
              Difference?
            </span>
          </h2>
          <p className="text-xl text-blue-100 mb-12 leading-relaxed max-w-2xl mx-auto">
            Join thousands of community members who are building resilience and helping neighbors in times of need.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/report/new"
              className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all transform hover:-translate-y-1 hover:shadow-2xl flex items-center justify-center border border-blue-400/20"
            >
              Report an Impact
              <AlertTriangle size={20} className="ml-3" />
            </Link>
            <ViewReportsButton 
              variant="outline" 
              size="lg" 
              className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
            />
          </div>
        </div>
      </section>

      {/* Chat Widget */}
      <ChatWidget position="bottom-right" />
    </div>
  );
};

export default Home;
