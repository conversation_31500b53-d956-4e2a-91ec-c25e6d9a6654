export interface AdminUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'moderator' | 'editor' | 'viewer';
  permissions: Permission[];
  lastLogin: Date;
  isActive: boolean;
  createdAt: Date;
  suspendedUntil?: Date;
  suspensionReason?: string;
  violationHistory: UserViolation[];
  loginHistory: LoginRecord[];
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'verify' | 'moderate' | 'suspend';
}

// User Management Types
export interface UserViolation {
  id: string;
  userId: string;
  type: 'spam' | 'inappropriate' | 'fake_report' | 'harassment' | 'other';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  reportedBy: string;
  reportedAt: Date;
  action: 'warning' | 'suspension' | 'blacklist' | 'none';
  actionBy?: string;
  actionAt?: Date;
  evidence: string[];
}

export interface LoginRecord {
  id: string;
  userId: string;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  location?: string;
  success: boolean;
  failureReason?: string;
}

export interface UserSuspension {
  id: string;
  userId: string;
  suspendedBy: string;
  reason: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  appealSubmitted?: boolean;
  appealReason?: string;
  appealDate?: Date;
}

export interface BlacklistRequest {
  id: string;
  userId: string;
  requestedBy: string;
  reason: string;
  evidence: string[];
  status: 'pending' | 'approved' | 'denied';
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
  createdAt: Date;
}

export interface ContentSection {
  id: string;
  type: 'hero' | 'features' | 'testimonials' | 'pricing' | 'stats' | 'partners';
  title: string;
  content: any;
  isPublished: boolean;
  scheduledAt?: Date;
  lastModified: Date;
  modifiedBy: string;
  version: number;
}

export interface AnalyticsData {
  visitors: {
    total: number;
    unique: number;
    returning: number;
    trend: number;
  };
  engagement: {
    avgSessionDuration: number;
    bounceRate: number;
    pageViews: number;
    trend: number;
  };
  conversions: {
    signups: number;
    reports: number;
    rate: number;
    trend: number;
  };
  traffic: {
    sources: Array<{ name: string; value: number; color: string }>;
    devices: Array<{ name: string; value: number }>;
    locations: Array<{ country: string; visitors: number }>;
  };
}

export interface DashboardMetrics {
  totalReports: number;
  verifiedReports: number;
  activeUsers: number;
  responseTime: string;
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical';
  recentActivity: ActivityLog[];
}

export interface ActivityLog {
  id: string;
  action: string;
  user: string;
  timestamp: Date;
  details: string;
  type: 'content' | 'user' | 'system' | 'security' | 'moderation' | 'verification';
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

// Report Verification Types
export interface ReportVerification {
  id: string;
  reportId: string;
  verifiedBy: string;
  status: 'verified' | 'fake' | 'pending' | 'needs_review';
  verificationDate: Date;
  reasonCodes: string[];
  evidence: VerificationEvidence[];
  externalDataChecked: ExternalDataSource[];
  confidence: number; // 0-100
  notes: string;
  reviewHistory: VerificationReview[];
}

export interface VerificationEvidence {
  id: string;
  type: 'photo_analysis' | 'location_verification' | 'timestamp_check' | 'external_data' | 'user_history' | 'manual_review';
  description: string;
  result: 'supports' | 'contradicts' | 'neutral';
  confidence: number;
  source: string;
  data: Record<string, any>;
}

export interface ExternalDataSource {
  id: string;
  name: string;
  type: 'usgs' | 'weather' | 'news' | 'social_media' | 'government';
  url: string;
  lastChecked: Date;
  status: 'verified' | 'no_data' | 'contradicts' | 'error';
  data?: Record<string, any>;
}

export interface VerificationReview {
  id: string;
  reviewedBy: string;
  reviewDate: Date;
  previousStatus: string;
  newStatus: string;
  reason: string;
  notes: string;
}

// Content Management Types
export interface ContentFlag {
  id: string;
  reportId: string;
  flaggedBy: string;
  flagType: 'inappropriate' | 'spam' | 'duplicate' | 'misinformation' | 'harassment' | 'other';
  description: string;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  reviewedBy?: string;
  reviewedAt?: Date;
  resolution?: string;
  createdAt: Date;
}

export interface ReportModeration {
  id: string;
  reportId: string;
  action: 'hide' | 'unhide' | 'flag' | 'unflag' | 'delete' | 'restore';
  moderatedBy: string;
  reason: string;
  timestamp: Date;
  isActive: boolean;
  notes?: string;
}

export interface AssistanceInteraction {
  id: string;
  reportId: string;
  providerId: string;
  providerName: string;
  type: 'message' | 'offer' | 'delivery' | 'completion';
  content: string;
  timestamp: Date;
  responseTime?: number; // in minutes
  status: 'pending' | 'acknowledged' | 'completed' | 'cancelled';
  moderatorNotes?: string;
}

// System Configuration Types
export interface DisasterCategory {
  id: string;
  name: string;
  type: 'Natural' | 'Non-Natural';
  description: string;
  icon: string;
  color: string;
  isActive: boolean;
  verificationCriteria: string[];
  requiredFields: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface APIConfiguration {
  id: string;
  name: string;
  type: 'usgs' | 'weather' | 'news' | 'social_media' | 'government';
  baseUrl: string;
  apiKey: string; // encrypted
  isActive: boolean;
  rateLimit: number;
  timeout: number;
  retryAttempts: number;
  lastTested: Date;
  testStatus: 'success' | 'failure' | 'pending';
  configuration: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityPolicy {
  id: string;
  name: string;
  type: 'password' | 'session' | 'access' | 'audit' | 'data_retention';
  settings: Record<string, any>;
  isActive: boolean;
  enforcedAt: 'user' | 'admin' | 'moderator' | 'all';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValue?: Record<string, any>;
  newValue?: Record<string, any>;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  sessionId: string;
  success: boolean;
  errorMessage?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Analytics and Metrics Types
export interface UserAnalytics {
  userId: string;
  totalReports: number;
  verifiedReports: number;
  rejectedReports: number;
  assistanceProvided: number;
  assistanceReceived: number;
  averageResponseTime: number;
  lastActivity: Date;
  engagementScore: number;
  trustScore: number;
  violationCount: number;
}

export interface SystemMetrics {
  id: string;
  timestamp: Date;
  activeUsers: number;
  totalReports: number;
  pendingVerifications: number;
  averageVerificationTime: number;
  systemLoad: number;
  responseTime: number;
  errorRate: number;
  storageUsed: number;
  bandwidthUsed: number;
}

export interface BackupConfiguration {
  id: string;
  name: string;
  type: 'full' | 'incremental' | 'differential';
  schedule: string; // cron expression
  retention: number; // days
  destination: string;
  encryption: boolean;
  compression: boolean;
  isActive: boolean;
  lastRun?: Date;
  lastStatus?: 'success' | 'failure' | 'running';
  nextRun?: Date;
}