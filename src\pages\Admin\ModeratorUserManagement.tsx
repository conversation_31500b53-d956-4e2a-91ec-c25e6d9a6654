import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  User, 
  Clock, 
  AlertTriangle,
  Shield,
  Ban,
  Flag,
  Eye,
  MoreVertical,
  Calendar,
  Activity,
  UserX,
  UserCheck,
  MessageSquare,
  FileText,
  TrendingUp,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { User as UserType, UserViolation, BlacklistRequest } from '../../types';

interface UserStats {
  totalUsers: number;
  suspendedUsers: number;
  flaggedUsers: number;
  pendingReviews: number;
  violationsToday: number;
  averageResponseTime: number;
}

interface ExtendedUser extends UserType {
  violations: UserViolation[];
  suspensionHistory: Array<{
    id: string;
    reason: string;
    duration: number;
    suspendedBy: string;
    suspendedAt: Date;
    expiresAt: Date;
    isActive: boolean;
  }>;
  flagHistory: Array<{
    id: string;
    reason: string;
    flaggedBy: string;
    flaggedAt: Date;
    status: 'pending' | 'reviewed' | 'dismissed';
  }>;
  reportCount: number;
  verificationRate: number;
  lastActivity: Date;
}

const ModeratorUserManagement: React.FC = () => {
  const [users, setUsers] = useState<ExtendedUser[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [riskFilter, setRiskFilter] = useState('all');
  const [showSuspensionModal, setShowSuspensionModal] = useState(false);
  const [showFlagModal, setShowFlagModal] = useState(false);
  const [selectedUserForAction, setSelectedUserForAction] = useState<string | null>(null);
  const [suspensionReason, setSuspensionReason] = useState('');
  const [suspensionDuration, setSuspensionDuration] = useState(24);
  const [flagReason, setFlagReason] = useState('');
  const [flagType, setFlagType] = useState('suspicious_activity');
  const [stats, setStats] = useState<UserStats>({
    totalUsers: 0,
    suspendedUsers: 0,
    flaggedUsers: 0,
    pendingReviews: 0,
    violationsToday: 0,
    averageResponseTime: 2.5
  });

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockUsers: ExtendedUser[] = [
      {
        id: '1',
        name: 'John Anderson',
        email: '<EMAIL>',
        role: 'user',
        isBlacklisted: false,
        trustScore: 85,
        createdAt: new Date('2023-06-15'),
        lastLoginAt: new Date('2024-01-20'),
        violations: [
          {
            id: 'v1',
            userId: '1',
            type: 'inappropriate_content',
            description: 'Posted inappropriate content in report comments',
            severity: 'medium',
            reportedBy: 'moderator-1',
            reportedAt: new Date('2024-01-10'),
            status: 'resolved',
            actionTaken: 'warning_issued'
          }
        ],
        suspensionHistory: [],
        flagHistory: [],
        reportCount: 12,
        verificationRate: 92,
        lastActivity: new Date('2024-01-20')
      },
      {
        id: '2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'user',
        isBlacklisted: false,
        trustScore: 95,
        createdAt: new Date('2023-03-20'),
        lastLoginAt: new Date('2024-01-19'),
        violations: [],
        suspensionHistory: [],
        flagHistory: [],
        reportCount: 28,
        verificationRate: 96,
        lastActivity: new Date('2024-01-19')
      },
      {
        id: '3',
        name: 'Mike Davis',
        email: '<EMAIL>',
        role: 'user',
        isBlacklisted: false,
        trustScore: 45,
        createdAt: new Date('2023-11-10'),
        lastLoginAt: new Date('2024-01-18'),
        violations: [
          {
            id: 'v2',
            userId: '3',
            type: 'false_reporting',
            description: 'Submitted multiple false disaster reports',
            severity: 'high',
            reportedBy: 'moderator-2',
            reportedAt: new Date('2024-01-15'),
            status: 'under_review',
            actionTaken: 'temporary_suspension'
          },
          {
            id: 'v3',
            userId: '3',
            type: 'spam',
            description: 'Excessive posting of duplicate reports',
            severity: 'medium',
            reportedBy: 'moderator-1',
            reportedAt: new Date('2024-01-12'),
            status: 'resolved',
            actionTaken: 'content_removed'
          }
        ],
        suspensionHistory: [
          {
            id: 's1',
            reason: 'Multiple false reports',
            duration: 72,
            suspendedBy: 'moderator-2',
            suspendedAt: new Date('2024-01-15'),
            expiresAt: new Date('2024-01-18'),
            isActive: false
          }
        ],
        flagHistory: [
          {
            id: 'f1',
            reason: 'Suspicious reporting pattern',
            flaggedBy: 'moderator-1',
            flaggedAt: new Date('2024-01-14'),
            status: 'reviewed'
          }
        ],
        reportCount: 8,
        verificationRate: 25,
        lastActivity: new Date('2024-01-18')
      },
      {
        id: '4',
        name: 'Lisa Wilson',
        email: '<EMAIL>',
        role: 'user',
        isBlacklisted: true,
        trustScore: 15,
        createdAt: new Date('2023-12-05'),
        lastLoginAt: new Date('2024-01-17'),
        violations: [
          {
            id: 'v4',
            userId: '4',
            type: 'harassment',
            description: 'Harassing other users in assistance comments',
            severity: 'high',
            reportedBy: 'moderator-3',
            reportedAt: new Date('2024-01-16'),
            status: 'resolved',
            actionTaken: 'account_blacklisted'
          }
        ],
        suspensionHistory: [
          {
            id: 's2',
            reason: 'Harassment and inappropriate behavior',
            duration: 168, // 7 days
            suspendedBy: 'moderator-3',
            suspendedAt: new Date('2024-01-16'),
            expiresAt: new Date('2024-01-23'),
            isActive: true
          }
        ],
        flagHistory: [
          {
            id: 'f2',
            reason: 'Multiple user complaints',
            flaggedBy: 'moderator-2',
            flaggedAt: new Date('2024-01-15'),
            status: 'reviewed'
          }
        ],
        reportCount: 3,
        verificationRate: 0,
        lastActivity: new Date('2024-01-17')
      }
    ];

    setUsers(mockUsers);
    setStats({
      totalUsers: mockUsers.length,
      suspendedUsers: mockUsers.filter(u => u.suspensionHistory.some(s => s.isActive)).length,
      flaggedUsers: mockUsers.filter(u => u.flagHistory.some(f => f.status === 'pending')).length,
      pendingReviews: mockUsers.filter(u => u.violations.some(v => v.status === 'under_review')).length,
      violationsToday: mockUsers.reduce((acc, u) => acc + u.violations.filter(v => 
        v.reportedAt.toDateString() === new Date().toDateString()
      ).length, 0),
      averageResponseTime: 2.5
    });
  }, []);

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && !user.isBlacklisted && !user.suspensionHistory.some(s => s.isActive)) ||
                         (statusFilter === 'suspended' && user.suspensionHistory.some(s => s.isActive)) ||
                         (statusFilter === 'blacklisted' && user.isBlacklisted);
    
    const matchesRisk = riskFilter === 'all' ||
                       (riskFilter === 'high' && user.trustScore < 50) ||
                       (riskFilter === 'medium' && user.trustScore >= 50 && user.trustScore < 80) ||
                       (riskFilter === 'low' && user.trustScore >= 80);
    
    return matchesSearch && matchesStatus && matchesRisk;
  });

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map(user => user.id));
    }
  };

  const handleSuspendUser = () => {
    if (!selectedUserForAction) return;

    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + suspensionDuration);

    setUsers(prev => prev.map(user => 
      user.id === selectedUserForAction 
        ? {
            ...user,
            suspensionHistory: [
              ...user.suspensionHistory,
              {
                id: `s-${Date.now()}`,
                reason: suspensionReason,
                duration: suspensionDuration,
                suspendedBy: 'current-moderator',
                suspendedAt: new Date(),
                expiresAt,
                isActive: true
              }
            ]
          }
        : user
    ));

    setShowSuspensionModal(false);
    setSelectedUserForAction(null);
    setSuspensionReason('');
    setSuspensionDuration(24);
  };

  const handleFlagUser = () => {
    if (!selectedUserForAction) return;

    setUsers(prev => prev.map(user => 
      user.id === selectedUserForAction 
        ? {
            ...user,
            flagHistory: [
              ...user.flagHistory,
              {
                id: `f-${Date.now()}`,
                reason: flagReason,
                flaggedBy: 'current-moderator',
                flaggedAt: new Date(),
                status: 'pending' as const
              }
            ]
          }
        : user
    ));

    setShowFlagModal(false);
    setSelectedUserForAction(null);
    setFlagReason('');
  };

  const getUserStatusBadge = (user: ExtendedUser) => {
    if (user.isBlacklisted) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">Blacklisted</span>;
    }
    if (user.suspensionHistory.some(s => s.isActive)) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200">Suspended</span>;
    }
    if (user.flagHistory.some(f => f.status === 'pending')) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">Flagged</span>;
    }
    return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">Active</span>;
  };

  const getTrustScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getRiskLevel = (user: ExtendedUser) => {
    if (user.trustScore < 50 || user.violations.length > 2) return 'High';
    if (user.trustScore < 80 || user.violations.length > 0) return 'Medium';
    return 'Low';
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'High': return 'text-red-600 bg-red-50';
      case 'Medium': return 'text-yellow-600 bg-yellow-50';
      case 'Low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">User Management</h1>
          <p className="text-gray-600">Monitor users, manage violations, and handle suspensions</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
            </div>
            <div className="p-3 bg-blue-100 text-blue-600 rounded-xl">
              <User size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Suspended</p>
              <p className="text-2xl font-bold text-gray-900">{stats.suspendedUsers}</p>
            </div>
            <div className="p-3 bg-orange-100 text-orange-600 rounded-xl">
              <Ban size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Flagged</p>
              <p className="text-2xl font-bold text-gray-900">{stats.flaggedUsers}</p>
            </div>
            <div className="p-3 bg-yellow-100 text-yellow-600 rounded-xl">
              <Flag size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pendingReviews}</p>
            </div>
            <div className="p-3 bg-purple-100 text-purple-600 rounded-xl">
              <Clock size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Violations Today</p>
              <p className="text-2xl font-bold text-gray-900">{stats.violationsToday}</p>
            </div>
            <div className="p-3 bg-red-100 text-red-600 rounded-xl">
              <AlertTriangle size={24} />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Response</p>
              <p className="text-2xl font-bold text-gray-900">{stats.averageResponseTime}h</p>
            </div>
            <div className="p-3 bg-green-100 text-green-600 rounded-xl">
              <TrendingUp size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search users..."
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="relative">
            <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="suspended">Suspended</option>
              <option value="blacklisted">Blacklisted</option>
            </select>
          </div>
          <div className="relative">
            <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={riskFilter}
              onChange={(e) => setRiskFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              <option value="all">All Risk Levels</option>
              <option value="high">High Risk</option>
              <option value="medium">Medium Risk</option>
              <option value="low">Low Risk</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="w-12 px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">User</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Status</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Trust Score</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Risk Level</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Reports</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Violations</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Last Activity</th>
                <th className="px-6 py-4 text-right text-sm font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleSelectUser(user.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <User size={20} className="text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{user.name}</p>
                        <p className="text-sm text-gray-600">{user.email}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    {getUserStatusBadge(user)}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <span className={`font-medium ${getTrustScoreColor(user.trustScore)}`}>
                        {user.trustScore}%
                      </span>
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            user.trustScore >= 80 ? 'bg-green-500' :
                            user.trustScore >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${user.trustScore}%` }}
                        />
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(getRiskLevel(user))}`}>
                      {getRiskLevel(user)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <span className="font-medium text-gray-900">{user.reportCount}</span>
                      <p className="text-gray-600">{user.verificationRate}% verified</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-1">
                      <AlertTriangle size={16} className="text-red-500" />
                      <span className="text-sm font-medium text-gray-900">{user.violations.length}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {format(user.lastActivity, 'MMM d, yyyy')}
                      <p className="text-gray-600">{format(user.lastActivity, 'HH:mm')}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => {
                          setSelectedUserForAction(user.id);
                          setShowSuspensionModal(true);
                        }}
                        className="p-2 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
                        title="Suspend user"
                      >
                        <Ban size={16} />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedUserForAction(user.id);
                          setShowFlagModal(true);
                        }}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Flag user"
                      >
                        <Flag size={16} />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                        <MoreVertical size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Suspension Modal */}
      {showSuspensionModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Suspend User</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Duration (hours)</label>
                <select
                  value={suspensionDuration}
                  onChange={(e) => setSuspensionDuration(parseInt(e.target.value))}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={1}>1 hour</option>
                  <option value={6}>6 hours</option>
                  <option value={24}>24 hours</option>
                  <option value={72}>3 days</option>
                  <option value={168}>1 week</option>
                  <option value={720}>30 days</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                <textarea
                  value={suspensionReason}
                  onChange={(e) => setSuspensionReason(e.target.value)}
                  placeholder="Provide a detailed reason for the suspension..."
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={4}
                />
              </div>
              <div className="flex space-x-3 pt-4">
                <button
                  onClick={handleSuspendUser}
                  className="flex-1 bg-orange-600 text-white py-3 rounded-xl hover:bg-orange-700 transition-colors"
                >
                  Suspend User
                </button>
                <button
                  onClick={() => {
                    setShowSuspensionModal(false);
                    setSelectedUserForAction(null);
                    setSuspensionReason('');
                  }}
                  className="flex-1 border border-gray-300 text-gray-700 py-3 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Flag Modal */}
      {showFlagModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Flag User for Admin Review</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Flag Type</label>
                <select
                  value={flagType}
                  onChange={(e) => setFlagType(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="suspicious_activity">Suspicious Activity</option>
                  <option value="policy_violation">Policy Violation</option>
                  <option value="harassment">Harassment</option>
                  <option value="spam_behavior">Spam Behavior</option>
                  <option value="false_reporting">False Reporting</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                <textarea
                  value={flagReason}
                  onChange={(e) => setFlagReason(e.target.value)}
                  placeholder="Provide details about why this user should be reviewed by an admin..."
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={4}
                />
              </div>
              <div className="flex space-x-3 pt-4">
                <button
                  onClick={handleFlagUser}
                  className="flex-1 bg-red-600 text-white py-3 rounded-xl hover:bg-red-700 transition-colors"
                >
                  Flag for Review
                </button>
                <button
                  onClick={() => {
                    setShowFlagModal(false);
                    setSelectedUserForAction(null);
                    setFlagReason('');
                  }}
                  className="flex-1 border border-gray-300 text-gray-700 py-3 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModeratorUserManagement;
